package com.midas.crm.repository;

import com.midas.crm.entity.Route;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RouteRepository extends JpaRepository<Route, Long> {

    Optional<Route> findByPath(String path);

    Optional<Route> findByPathAndIsActiveTrue(String path);

    List<Route> findByIsActiveTrue();

    Page<Route> findByIsActiveTrue(Pageable pageable);

    @Query("SELECT r FROM Route r WHERE r.isActive = true AND " +
            "(LOWER(r.name) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(r.path) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(r.description) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<Route> findByQueryAndIsActiveTrue(@Param("query") String query, Pageable pageable);

    @Query("SELECT r FROM Route r WHERE " +
            "(LOWER(r.name) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(r.path) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(r.description) LIKE LOWER(CONCAT('%', :query, '%')))")
    Page<Route> findByQuery(@Param("query") String query, Pageable pageable);

    boolean existsByPath(String path);
}
