package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.User;
import com.midas.crm.entity.DTO.curso.CursoCreateDTO;
import com.midas.crm.entity.DTO.curso.CursoDTO;
import com.midas.crm.entity.DTO.curso.CursoUpdateDTO;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.CursoMapper;
import com.midas.crm.repository.CursoRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.CursoService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CursoServiceImpl implements CursoService {

    private final CursoRepository cursoRepository;
    private final UserRepository userRepository;

    @Override
    public CursoDTO createCurso(CursoCreateDTO dto) {
        if (cursoRepository.existsByNombre(dto.getNombre())) {
            throw new MidasExceptions(MidasErrorMessage.CURSO_ALREADY_EXISTS);
        }

        Curso curso = new Curso();
        curso.setNombre(dto.getNombre());
        curso.setDescripcion(dto.getDescripcion());
        curso.setFechaInicio(dto.getFechaInicio());
        curso.setFechaFin(dto.getFechaFin());
        curso.setEstado("A");
        curso.setVideoUrl(dto.getVideoUrl());

        // 🔥 Si envía usuarioId, lo asignamos
        if (dto.getUsuarioId() != null) {
            User usuario = userRepository.findById(dto.getUsuarioId())
                    .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_USUARIO_NOT_FOUND));
            curso.setUsuario(usuario);
        }

        return CursoMapper.toDTO(cursoRepository.save(curso));
    }

    @Override
    public List<CursoDTO> listCursos() {
        return cursoRepository.findAll()
                .stream()
                .map(CursoMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public CursoDTO getCursoById(Long id) {
        return cursoRepository.findById(id)
                .map(CursoMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));
    }

    @Override
    public CursoDTO updateCurso(Long id, CursoUpdateDTO dto) {
        Curso curso = cursoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        CursoMapper.updateEntity(curso, dto);

        return CursoMapper.toDTO(cursoRepository.save(curso));
    }

    @Override
    public void deleteCurso(Long id) {
        Curso curso = cursoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        cursoRepository.delete(curso);
    }

    @Override
    public List<CursoDTO> getCursosByIds(List<Long> ids) {
        List<Curso> cursos = cursoRepository.findAllById(ids);
        return cursos.stream()
                .map(CursoMapper::toDTO)
                .collect(Collectors.toList());
    }
}
