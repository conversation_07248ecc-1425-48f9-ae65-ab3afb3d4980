package com.midas.crm.entity.DTO.websocket;

import com.midas.crm.entity.DTO.user.UserResponseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO para mensajes WebSocket relacionados con usuarios
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserWebSocketDTO {
    
    /**
     * Tipo de operación realizada
     * Valores posibles: CREATED, UPDATED, DELETED, LIST
     */
    private String operation;
    
    /**
     * Datos del usuario afectado por la operación
     */
    private UserResponseDTO user;
    
    /**
     * Mensaje descriptivo de la operación
     */
    private String message;
    
    /**
     * Crea un mensaje WebSocket para una operación de creación
     */
    public static UserWebSocketDTO created(UserResponseDTO user) {
        return new UserWebSocketDTO("CREATED", user, "Usuario creado exitosamente");
    }
    
    /**
     * Crea un mensaje WebSocket para una operación de actualización
     */
    public static UserWebSocketDTO updated(UserResponseDTO user) {
        return new UserWebSocketDTO("UPDATED", user, "Usuario actualizado exitosamente");
    }
    
    /**
     * Crea un mensaje WebSocket para una operación de eliminación
     */
    public static UserWebSocketDTO deleted(UserResponseDTO user) {
        return new UserWebSocketDTO("DELETED", user, "Usuario eliminado exitosamente");
    }
}
