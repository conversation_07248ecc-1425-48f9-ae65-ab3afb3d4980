package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.PreguntaEncuesta;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO para transferir información de preguntas de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaEncuestaDTO {
    private Long id;
    private String enunciado;
    private String descripcion;
    private Integer orden;
    private PreguntaEncuesta.TipoPregunta tipo;
    private Boolean esObligatoria;
    private Long encuestaId;
    private List<OpcionRespuestaEncuestaDTO> opciones;
    private String estado;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
    
    // Estadísticas (para respuestas)
    private Long totalRespuestas;
    private Double promedioRespuestasNumericas;
}
