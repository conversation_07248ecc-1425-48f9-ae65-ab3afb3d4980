package com.midas.crm.entity.DTO.cuestionario;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaUsuarioDTO {
    private Long id;
    private Long respuestaUsuarioId;
    private Long preguntaId;
    private String preguntaEnunciado;
    private Long respuestaId;
    private String respuestaTexto;
    private String textoRespuesta;
    private Boolean esCorrecta;
    private Integer puntajeObtenido;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
