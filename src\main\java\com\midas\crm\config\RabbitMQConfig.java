package com.midas.crm.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuración de RabbitMQ para el sistema de colas de transcripción
 */
@Configuration
public class RabbitMQConfig {

    // Nombres de las colas
    public static final String TRANSCRIPTION_QUEUE = "transcription.queue";
    public static final String TRANSCRIPTION_DLQ = "transcription.dlq";
    public static final String COMPARISON_QUEUE = "comparison.queue";
    public static final String COMPARISON_DLQ = "comparison.dlq";

    // Nombres de los exchanges
    public static final String TRANSCRIPTION_EXCHANGE = "transcription.exchange";
    public static final String COMPARISON_EXCHANGE = "comparison.exchange";
    public static final String DLX_EXCHANGE = "dlx.exchange";

    // Routing keys
    public static final String TRANSCRIPTION_ROUTING_KEY = "transcription.process";
    public static final String COMPARISON_ROUTING_KEY = "comparison.process";
    public static final String DLQ_ROUTING_KEY = "dlq";

    /**
     * Configuración del convertidor de mensajes JSON
     */
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * Configuración del RabbitTemplate con convertidor JSON
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(jsonMessageConverter());
        return template;
    }

    /**
     * Configuración del listener container factory
     */
    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(jsonMessageConverter());
        factory.setConcurrentConsumers(3); // Procesar máximo 3 transcripciones simultáneamente
        factory.setMaxConcurrentConsumers(3);
        factory.setPrefetchCount(1); // Procesar un mensaje a la vez por consumidor
        return factory;
    }

    // ========== EXCHANGES ==========

    /**
     * Exchange principal para transcripciones
     */
    @Bean
    public DirectExchange transcriptionExchange() {
        return new DirectExchange(TRANSCRIPTION_EXCHANGE, true, false);
    }

    /**
     * Exchange para comparaciones
     */
    @Bean
    public DirectExchange comparisonExchange() {
        return new DirectExchange(COMPARISON_EXCHANGE, true, false);
    }

    /**
     * Exchange para Dead Letter Queue
     */
    @Bean
    public DirectExchange dlxExchange() {
        return new DirectExchange(DLX_EXCHANGE, true, false);
    }

    // ========== COLAS PRINCIPALES ==========

    /**
     * Cola principal de transcripciones
     */
    @Bean
    public Queue transcriptionQueue() {
        return QueueBuilder.durable(TRANSCRIPTION_QUEUE)
                .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 1800000) // 30 minutos TTL
                .build();
    }

    /**
     * Cola principal de comparaciones
     */
    @Bean
    public Queue comparisonQueue() {
        return QueueBuilder.durable(COMPARISON_QUEUE)
                .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", DLQ_ROUTING_KEY)
                .withArgument("x-message-ttl", 600000) // 10 minutos TTL
                .build();
    }

    // ========== DEAD LETTER QUEUES ==========

    /**
     * Dead Letter Queue para transcripciones fallidas
     */
    @Bean
    public Queue transcriptionDlq() {
        return QueueBuilder.durable(TRANSCRIPTION_DLQ).build();
    }

    /**
     * Dead Letter Queue para comparaciones fallidas
     */
    @Bean
    public Queue comparisonDlq() {
        return QueueBuilder.durable(COMPARISON_DLQ).build();
    }

    // ========== BINDINGS ==========

    /**
     * Binding para cola de transcripciones
     */
    @Bean
    public Binding transcriptionBinding() {
        return BindingBuilder
                .bind(transcriptionQueue())
                .to(transcriptionExchange())
                .with(TRANSCRIPTION_ROUTING_KEY);
    }

    /**
     * Binding para cola de comparaciones
     */
    @Bean
    public Binding comparisonBinding() {
        return BindingBuilder
                .bind(comparisonQueue())
                .to(comparisonExchange())
                .with(COMPARISON_ROUTING_KEY);
    }

    /**
     * Binding para DLQ de transcripciones
     */
    @Bean
    public Binding transcriptionDlqBinding() {
        return BindingBuilder
                .bind(transcriptionDlq())
                .to(dlxExchange())
                .with(DLQ_ROUTING_KEY);
    }

    /**
     * Binding para DLQ de comparaciones
     */
    @Bean
    public Binding comparisonDlqBinding() {
        return BindingBuilder
                .bind(comparisonDlq())
                .to(dlxExchange())
                .with(DLQ_ROUTING_KEY);
    }
}
