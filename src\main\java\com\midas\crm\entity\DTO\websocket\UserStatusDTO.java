package com.midas.crm.entity.DTO.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para representar el estado de conexión de un usuario
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserStatusDTO {
    private Long userId;
    private String username;
    private String nombre;
    private String apellido;
    private String status; // "ONLINE" o "OFFLINE"
    private boolean online; // Campo adicional para compatibilidad con el frontend
    private LocalDateTime lastActivity;

    // Método para establecer el estado y el campo online de forma consistente
    public void setStatus(String status) {
        this.status = status;
        this.online = "ONLINE".equals(status);
    }
}
