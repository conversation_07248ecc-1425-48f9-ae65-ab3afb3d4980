package com.midas.crm.entity.DTO.modulo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModuloCreateDTO {
    @NotBlank(message = "El título del módulo es obligatorio")
    private String titulo;

    private String descripcion;

    @NotNull(message = "El orden del módulo es obligatorio")
    private Integer orden;

    @NotNull(message = "El ID del curso es obligatorio")
    private Long cursoId;
}
