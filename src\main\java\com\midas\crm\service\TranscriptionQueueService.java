package com.midas.crm.service;

import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;

import java.util.List;
import java.util.Map;

/**
 * Servicio para gestionar las colas de transcripción automática
 */
public interface TranscriptionQueueService {

    /**
     * Procesa leads pendientes de transcripción y los envía a la cola
     * @param batchSize Número máximo de leads a procesar
     * @param numeroAgente Filtro opcional por número de agente
     * @return Mapa con estadísticas del procesamiento
     */
    Map<String, Object> processPendingLeads(int batchSize, String numeroAgente);

    /**
     * Obtiene estadísticas de las colas de transcripción
     * @return Mapa con estadísticas de las colas
     */
    Map<String, Object> getQueueStatistics();

    /**
     * Obtiene la lista de leads pendientes de transcripción
     * @param limit Número máximo de leads a retornar
     * @param numeroAgente Filtro opcional por número de agente
     * @return Lista de mensajes de transcripción pendientes
     */
    List<TranscriptionQueueMessage> getPendingLeads(int limit, String numeroAgente);

    /**
     * Reintenta el procesamiento de mensajes fallidos
     * @param maxRetries Número máximo de mensajes a reintentar
     * @return Mapa con estadísticas del reintento
     */
    Map<String, Object> retryFailedMessages(int maxRetries);

    /**
     * Envía un lead específico a la cola de transcripción
     * @param leadId ID del lead a enviar
     * @return true si se envió correctamente, false en caso contrario
     */
    boolean sendLeadToQueue(Long leadId);

    /**
     * Pausa el procesamiento de las colas
     */
    void pauseProcessing();

    /**
     * Reanuda el procesamiento de las colas
     */
    void resumeProcessing();

    /**
     * Procesa un mensaje de transcripción (llamado por el listener de RabbitMQ)
     * @param message Mensaje de transcripción a procesar
     */
    void processTranscriptionMessage(TranscriptionQueueMessage message);

    /**
     * Procesa un mensaje de comparación (llamado por el listener de RabbitMQ)
     * @param message Mensaje de comparación a procesar
     */
    void processComparisonMessage(TranscriptionQueueMessage message);

    /**
     * Normaliza el número de agente para manejar diferentes formatos
     * @param numeroAgente Número de agente original
     * @return Número de agente normalizado
     */
    String normalizeAgentNumber(String numeroAgente);

    /**
     * Verifica si un lead cumple los criterios para ser procesado
     * @param leadId ID del lead a verificar
     * @return true si cumple los criterios, false en caso contrario
     */
    boolean isLeadEligibleForProcessing(Long leadId);

    /**
     * Obtiene el estado actual del procesamiento de colas
     * @return true si está activo, false si está pausado
     */
    boolean isProcessingActive();
}
