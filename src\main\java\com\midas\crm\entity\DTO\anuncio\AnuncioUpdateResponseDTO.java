package com.midas.crm.entity.DTO.anuncio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AnuncioUpdateResponseDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private String imagenUrl;
    private String categoria;
    private LocalDateTime fechaPublicacion;
    private LocalDateTime fechaInicio;
    private LocalDateTime fechaFin;
    private Integer orden;
    private String estado;
    private Long usuarioId;
    private Long sedeId;
    private String nombreSede;

}
