package com.midas.crm.service;

import com.midas.crm.entity.DTO.cuestionario.CuestionarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioDTO;
import com.midas.crm.entity.DTO.cuestionario.CuestionarioUpdateDTO;

import java.util.List;

public interface CuestionarioService {
    CuestionarioDTO createCuestionario(CuestionarioCreateDTO dto);
    List<CuestionarioDTO> listCuestionarios();
    CuestionarioDTO getCuestionarioById(Long id);
    CuestionarioDTO getCuestionarioByLeccionId(Long leccionId);
    CuestionarioDTO updateCuestionario(Long id, CuestionarioUpdateDTO dto);
    void deleteCuestionario(Long id);
}
