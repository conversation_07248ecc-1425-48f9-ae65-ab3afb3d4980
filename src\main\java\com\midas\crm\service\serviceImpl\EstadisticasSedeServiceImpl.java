package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.EstadisticaSedeDTO;
import com.midas.crm.entity.DTO.EstadisticaSedePaginadaResponse;
import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.ClienteResidencialExcelService;
import com.midas.crm.service.EstadisticasSedeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Implementación del servicio de estadísticas por sede
 */
@Service
public class EstadisticasSedeServiceImpl implements EstadisticasSedeService {

    @Autowired
    private ClienteResidencialRepository clienteResidencialRepository;

    @Autowired
    private UserRepository userRepository;

    @Override
    public List<EstadisticaSedeDTO> obtenerEstadisticasPorSede(Long sedeId, LocalDate fecha) {
        if (sedeId != null) {
            return clienteResidencialRepository.obtenerEstadisticasPorSedeYFecha(sedeId, fecha);
        } else {
            return clienteResidencialRepository.obtenerEstadisticasPorFechaSinPaginacion(fecha);
        }
    }

    @Override
    public List<EstadisticaSedeDTO> obtenerResumenPorSede(LocalDate fecha) {
        return clienteResidencialRepository.obtenerResumenPorSedeYFecha(fecha);
    }

    @Override
    public List<EstadisticaSedeDTO> obtenerEstadisticasPorRango(LocalDate fechaInicio, LocalDate fechaFin,
                                                                Long sedeId) {
        if (sedeId != null) {
            return clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio, fechaFin);
        } else {
            return clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
        }
    }

    // ===== IMPLEMENTACIÓN DE MÉTODOS PAGINADOS =====

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginado(Long sedeId, Long supervisorId,
                                                                              LocalDate fecha,
                                                                              Pageable pageable) {

        Page<EstadisticaSedeDTO> page;

        // Determinar qué método usar según los filtros
        if (sedeId != null && supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorPaginado(sedeId, supervisorId,
                    fecha, pageable);
        } else if (sedeId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYFechaPaginado(sedeId, fecha, pageable);
        } else if (supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSupervisorPaginado(supervisorId, fecha, pageable);
        } else {
            page = clienteResidencialRepository.obtenerEstadisticasPorFecha(fecha, pageable);
        }

        EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                page.getContent(),
                page.getNumber(),
                page.getTotalPages(),
                page.getTotalElements(),
                page.getSize());

        return response;
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorSedePaginadoConBusqueda(Long sedeId, Long supervisorId,
                                                                                         LocalDate fecha, String busquedaVendedor,
                                                                                         Pageable pageable) {

        Page<EstadisticaSedeDTO> page;

        // Determinar qué método usar según los filtros
        if (sedeId != null && supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorConBusquedaPaginado(sedeId,
                    supervisorId,
                    fecha, busquedaVendedor, pageable);
        } else if (sedeId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSedeYFechaConBusquedaPaginado(sedeId, fecha,
                    busquedaVendedor, pageable);
        } else if (supervisorId != null) {
            page = clienteResidencialRepository.obtenerEstadisticasPorSupervisorConBusquedaPaginado(supervisorId, fecha,
                    busquedaVendedor, pageable);
        } else {
            page = clienteResidencialRepository.obtenerEstadisticasPorFechaConBusqueda(fecha, busquedaVendedor,
                    pageable);
        }

        EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                page.getContent(),
                page.getNumber(),
                page.getTotalPages(),
                page.getTotalElements(),
                page.getSize());

        return response;
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoPaginado(LocalDate fechaInicio,
                                                                               LocalDate fechaFin, Long sedeId, Pageable pageable) {
        // Para el rango, usamos el método sin paginación por ahora
        // Se puede implementar una versión paginada específica si es necesario
        List<EstadisticaSedeDTO> estadisticas = obtenerEstadisticasPorRango(fechaInicio, fechaFin, sedeId);

        // Simular paginación manual
        int page = pageable.getPageNumber();
        int size = pageable.getPageSize();
        int start = page * size;
        int end = Math.min(start + size, estadisticas.size());

        List<EstadisticaSedeDTO> pageContent = estadisticas.subList(start, end);
        int totalPages = (int) Math.ceil((double) estadisticas.size() / size);

        return new EstadisticaSedePaginadaResponse(
                pageContent,
                page,
                totalPages,
                estadisticas.size(),
                size);
    }

    @Override
    public Map<String, Object> obtenerLeadsPorAsesorYFecha(String nombreAsesor, LocalDate fecha, String numeroMovil,
                                                           Pageable pageable) {
        // Usar el método existente del repositorio para obtener clientes filtrados
        Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository.obtenerClientesConUsuarioFiltrados(
                null, // dniAsesor
                nombreAsesor, // nombreAsesor
                numeroMovil, // numeroMovil
                fecha, // fecha
                pageable);

        // Crear respuesta con formato similar al usado en ClienteResidencialService
        Map<String, Object> response = new HashMap<>();
        response.put("clientes", pageClientes.getContent());
        response.put("currentPage", pageClientes.getNumber());
        response.put("totalItems", pageClientes.getTotalElements());
        response.put("totalPages", pageClientes.getTotalPages());
        response.put("asesor", nombreAsesor);
        response.put("fecha", fecha.toString());

        return response;
    }

    @Override
    public Map<String, Object> obtenerLeadsPorAsesorYRangoFechas(String nombreAsesor, LocalDate fechaInicio,
                                                                 LocalDate fechaFin, String numeroMovil, Pageable pageable) {
        // Usar el método existente del repositorio para obtener clientes filtrados por
        // rango de fechas
        Page<ClienteConUsuarioDTO> pageClientes = clienteResidencialRepository
                .obtenerClientesConUsuarioFiltradosPorRango(
                        null, // dniAsesor
                        nombreAsesor, // nombreAsesor
                        numeroMovil, // numeroMovil
                        fechaInicio, // fechaInicio
                        fechaFin, // fechaFin
                        pageable);

        // Crear el resultado con la estructura esperada
        Map<String, Object> resultado = new HashMap<>();
        resultado.put("clientes", pageClientes.getContent());
        resultado.put("currentPage", pageClientes.getNumber());
        resultado.put("totalPages", pageClientes.getTotalPages());
        resultado.put("totalItems", pageClientes.getTotalElements());
        resultado.put("pageSize", pageClientes.getSize());
        resultado.put("hasNext", pageClientes.hasNext());
        resultado.put("hasPrevious", pageClientes.hasPrevious());

        return resultado;
    }

    @Override
    public List<Map<String, Object>> obtenerSupervisoresPorSede(Long sedeId) {
        // Obtener coordinadores (supervisores) de la sede específica
        List<User> coordinadores = userRepository.findBySedeIdAndRole(sedeId, Role.COORDINADOR);

        // Convertir a Map para el frontend
        List<Map<String, Object>> supervisores = coordinadores.stream()
                .map(coordinador -> {
                    Map<String, Object> supervisor = new HashMap<>();
                    supervisor.put("id", coordinador.getId());
                    supervisor.put("nombre", coordinador.getNombre() + " " + coordinador.getApellido());
                    supervisor.put("username", coordinador.getUsername());
                    return supervisor;
                })
                .collect(Collectors.toList());

        return supervisores;
    }

    @Autowired
    private ClienteResidencialExcelService clienteResidencialExcelService;

    @Override
    public byte[] exportarLeadsPorRango(Long sedeId, Long supervisorId, LocalDate fecha) {
        try {
            // Usar el método ULTRA-RÁPIDO que hace una sola consulta directa
            // Similar al método de rango de fechas que funciona muy rápido
            return clienteResidencialExcelService.generarExcelEstadisticasOptimizado(sedeId, supervisorId, fecha);

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel optimizado: " + e.getMessage(), e);
        }
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechas(Long sedeId, Long supervisorId,
                                                                             LocalDate fechaInicio, LocalDate fechaFin, Pageable pageable) {

        try {
            // Obtener estadísticas acumuladas por rango de fechas
            List<EstadisticaSedeDTO> estadisticasAcumuladas = obtenerEstadisticasAcumuladasPorRango(
                    sedeId, supervisorId, fechaInicio, fechaFin);

            // Aplicar paginación manual
            int totalElementos = estadisticasAcumuladas.size();
            int inicio = (int) pageable.getOffset();
            int fin = Math.min(inicio + pageable.getPageSize(), totalElementos);

            List<EstadisticaSedeDTO> estadisticasPaginadas = estadisticasAcumuladas.subList(inicio, fin);

            // Crear respuesta paginada usando el constructor existente
            EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                    estadisticasPaginadas,
                    pageable.getPageNumber(),
                    (int) Math.ceil((double) totalElementos / pageable.getPageSize()),
                    totalElementos,
                    pageable.getPageSize());

            return response;

        } catch (Exception e) {
            throw new RuntimeException("Error al obtener estadísticas por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public EstadisticaSedePaginadaResponse obtenerEstadisticasPorRangoFechasConBusqueda(Long sedeId, Long supervisorId,
                                                                                        LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor, Pageable pageable) {

        try {
            // Obtener estadísticas acumuladas por rango de fechas con búsqueda
            List<EstadisticaSedeDTO> estadisticasAcumuladas = obtenerEstadisticasAcumuladasPorRangoConBusqueda(
                    sedeId, supervisorId, fechaInicio, fechaFin, busquedaVendedor);

            // Aplicar paginación manual
            int totalElementos = estadisticasAcumuladas.size();
            int inicio = (int) pageable.getOffset();
            int fin = Math.min(inicio + pageable.getPageSize(), totalElementos);

            List<EstadisticaSedeDTO> estadisticasPaginadas = estadisticasAcumuladas.subList(inicio, fin);

            // Crear respuesta paginada usando el constructor existente
            EstadisticaSedePaginadaResponse response = new EstadisticaSedePaginadaResponse(
                    estadisticasPaginadas,
                    pageable.getPageNumber(),
                    (int) Math.ceil((double) totalElementos / pageable.getPageSize()),
                    totalElementos,
                    pageable.getPageSize());

            return response;

        } catch (Exception e) {
            throw new RuntimeException(
                    "Error al obtener estadísticas por rango de fechas con búsqueda: " + e.getMessage(), e);
        }
    }

    /**
     * Método auxiliar para obtener estadísticas acumuladas por rango de fechas
     * OPTIMIZADO: Usa consultas SQL directas en lugar de acumulación manual
     * CORREGIDO: Ahora respeta el filtro de supervisorId
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasAcumuladasPorRango(Long sedeId, Long supervisorId,
                                                                           LocalDate fechaInicio, LocalDate fechaFin) {

        // Usar las consultas SQL optimizadas que respetan TODOS los filtros
        List<EstadisticaSedeDTO> estadisticas;

        if (sedeId != null && supervisorId != null) {
            // Filtro por sede Y supervisor específicos
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorYRango(
                    sedeId, supervisorId, fechaInicio, fechaFin);
        } else if (sedeId != null) {
            // Solo filtro por sede
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio, fechaFin);
        } else if (supervisorId != null) {
            // Solo filtro por supervisor
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSupervisorYRango(
                    supervisorId, fechaInicio, fechaFin);
        } else {
            // Sin filtros específicos
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    /**
     * Método auxiliar para obtener estadísticas de una fecha específica
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasPorFecha(Long sedeId, Long supervisorId, LocalDate fecha) {
        List<EstadisticaSedeDTO> estadisticas;

        // Usar los métodos existentes del repositorio
        if (sedeId != null) {
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYFecha(sedeId, fecha);
        } else {
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorFechaSinPaginacion(fecha);
        }

        // Si hay filtro de supervisor, filtrar los resultados
        if (supervisorId != null && estadisticas != null) {
            // Por ahora, simplemente logueamos que se aplicaría el filtro
            // El filtro de supervisor se manejará en el frontend o en consultas más
            // específicas
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    /**
     * Método auxiliar para obtener estadísticas acumuladas por rango de fechas con
     * búsqueda
     * OPTIMIZADO: Usa consultas SQL directas con filtro de búsqueda
     * CORREGIDO: Ahora respeta el filtro de supervisorId
     */
    private List<EstadisticaSedeDTO> obtenerEstadisticasAcumuladasPorRangoConBusqueda(Long sedeId, Long supervisorId,
                                                                                      LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {

        // Usar las consultas SQL optimizadas con búsqueda que respetan TODOS los
        // filtros
        List<EstadisticaSedeDTO> estadisticas;

        if (sedeId != null && supervisorId != null) {
            // Filtro por sede Y supervisor específicos con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYSupervisorYRangoConBusqueda(
                    sedeId, supervisorId, fechaInicio, fechaFin, busquedaVendedor);
        } else if (sedeId != null) {
            // Solo filtro por sede con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRangoConBusqueda(sedeId, fechaInicio,
                    fechaFin, busquedaVendedor);
        } else if (supervisorId != null) {
            // Solo filtro por supervisor con búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSupervisorYRangoConBusqueda(
                    supervisorId, fechaInicio, fechaFin, busquedaVendedor);
        } else {
            // Sin filtros específicos, solo búsqueda
            estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRangoConBusqueda(fechaInicio, fechaFin,
                    busquedaVendedor);
        }

        return estadisticas != null ? estadisticas : new ArrayList<>();
    }

    @Override
    public byte[] exportarLeadsPorRangoFechas(Long sedeId, Long supervisorId, LocalDate fechaInicio,
                                              LocalDate fechaFin) {
        try {
            // Usar el método ULTRA-RÁPIDO de rango de fechas que ya existe y funciona
            // perfecto
            // Aplicando filtros de sede y supervisor
            return clienteResidencialExcelService.generarExcelEstadisticasPorRangoFechas(sedeId, supervisorId,
                    fechaInicio, fechaFin);

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel por rango de fechas: " + e.getMessage(), e);
        }
    }

    @Override
    public byte[] exportarLeadsFiltrados(Long sedeId, Long supervisorId, LocalDate fecha,
                                         LocalDate fechaInicio, LocalDate fechaFin, String busquedaVendedor) {
        try {
            // Determinar si es exportación por fecha específica o por rango
            if (fecha != null) {
                // Exportación por fecha específica con filtro de vendedor
                return clienteResidencialExcelService.generarExcelEstadisticasFiltrado(
                        sedeId, supervisorId, fecha, null, null, busquedaVendedor);
            } else if (fechaInicio != null && fechaFin != null) {
                // Exportación por rango de fechas con filtro de vendedor
                return clienteResidencialExcelService.generarExcelEstadisticasFiltrado(
                        sedeId, supervisorId, null, fechaInicio, fechaFin, busquedaVendedor);
            } else {
                throw new IllegalArgumentException("Debe proporcionar una fecha específica o un rango de fechas");
            }

        } catch (Exception e) {
            throw new RuntimeException("Error al generar el archivo Excel filtrado: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, Object>> obtenerRendimientoLeadsPorAsesor(String periodo, Long sedeId, Long supervisorId,
                                                                      LocalDate fecha) {
        try {
            // Calcular fechas según el período
            LocalDate fechaInicio;
            LocalDate fechaFin;

            switch (periodo.toLowerCase()) {
                case "diario":
                    fechaInicio = fecha;
                    fechaFin = fecha;
                    break;
                case "semanal":
                    // Obtener el lunes de la semana de la fecha dada
                    fechaInicio = fecha.with(java.time.DayOfWeek.MONDAY);
                    fechaFin = fechaInicio.plusDays(6); // Domingo
                    break;
                case "mensual":
                    // Primer y último día del mes
                    fechaInicio = fecha.withDayOfMonth(1);
                    fechaFin = fecha.withDayOfMonth(fecha.lengthOfMonth());
                    break;
                default:
                    throw new IllegalArgumentException("Período inválido: " + periodo);
            }

            // Obtener estadísticas para el rango calculado
            List<EstadisticaSedeDTO> estadisticas;
            if (sedeId != null) {
                estadisticas = clienteResidencialRepository.obtenerEstadisticasPorSedeYRango(sedeId, fechaInicio,
                        fechaFin);
            } else {
                estadisticas = clienteResidencialRepository.obtenerEstadisticasPorRango(fechaInicio, fechaFin);
            }

            // Filtrar por supervisor si se especifica
            if (supervisorId != null && estadisticas != null) {
                // Aquí se podría implementar el filtro por supervisor
                // Por ahora, mantenemos todos los datos
            }

            // Convertir a formato de respuesta para el frontend
            List<Map<String, Object>> resultado = new ArrayList<>();

            if (estadisticas != null) {
                for (EstadisticaSedeDTO estadistica : estadisticas) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("nombreAsesor", estadistica.getVendedor());
                    item.put("sede", estadistica.getSede());
                    item.put("supervisor", estadistica.getSupervisor());
                    item.put("totalLeads", estadistica.getTomaDatos());
                    item.put("periodo", periodo);
                    item.put("fechaInicio", fechaInicio.toString());
                    item.put("fechaFin", fechaFin.toString());

                    resultado.add(item);
                }
            }

            // Ordenar por total de leads descendente
            resultado.sort((a, b) -> {
                Integer leadsA = (Integer) a.get("totalLeads");
                Integer leadsB = (Integer) b.get("totalLeads");
                return leadsB.compareTo(leadsA);
            });

            return resultado;

        } catch (Exception e) {
            return new ArrayList<>();
        }
    }
}
