package com.midas.crm.entity.DTO.cliente;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ClienteConUsuarioDTO {
    private String dni;
    private String asesor;
    private LocalDateTime fechaIngresado;
    private String numeroMovil;
    private String coordinador;

    // Constructor original para mantener compatibilidad con código existente
    public ClienteConUsuarioDTO(String dni, String asesor, LocalDateTime fechaIngresado, String numeroMovil) {
        this.dni = dni;
        this.asesor = asesor;
        this.fechaIngresado = fechaIngresado;
        this.numeroMovil = numeroMovil;
    }

    // Constructor con coordinador
    public ClienteConUsuarioDTO(String dni, String asesor, LocalDateTime fechaIngresado, String numeroMovil, String coordinador) {
        this.dni = dni;
        this.asesor = asesor;
        this.fechaIngresado = fechaIngresado;
        this.numeroMovil = numeroMovil;
        this.coordinador = coordinador;
    }
}
