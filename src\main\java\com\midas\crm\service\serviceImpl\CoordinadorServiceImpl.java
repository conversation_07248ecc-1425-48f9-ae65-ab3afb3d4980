package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.asesor.AsignacionAsesorDTO;
import com.midas.crm.entity.DTO.coordinador.CoordinadorDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.AsesorMapper;
import com.midas.crm.mapper.CoordinadorMapper;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.repository.CoordinadorClienteRepository;
import com.midas.crm.service.CoordinadorService;
import com.midas.crm.utils.GenericResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CoordinadorServiceImpl implements CoordinadorService {

    private final UserRepository userRepository;
    private final CoordinadorClienteRepository coordinadorClienteRepository;

    @Override
    @Transactional
    public CoordinadorDTO asignarAsesoresACoordinador(AsignacionAsesorDTO asignacionDTO) {
        // Verificar que el coordinador existe
        User coordinador = userRepository.findById(asignacionDTO.getCoordinadorId())
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado"));

        // Verificar que el usuario es un coordinador
        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario no es un coordinador");
        }

        // Obtener los asesores a asignar
        List<User> asesores = userRepository.findAllById(asignacionDTO.getAsesorIds());

        // Verificar que todos los asesores existen
        if (asesores.size() != asignacionDTO.getAsesorIds().size()) {
            throw new RuntimeException("Uno o más asesores no fueron encontrados");
        }

        // Verificar que todos son asesores y asignarles el coordinador
        for (User asesor : asesores) {
            if (asesor.getRole() != Role.ASESOR) {
                throw new RuntimeException("El usuario " + asesor.getUsername() + " no es un asesor");
            }
            asesor.setCoordinador(coordinador);
            userRepository.save(asesor);
        }

        // Recargar el coordinador para obtener la lista actualizada de asesores
        coordinador = userRepository.findById(coordinador.getId()).orElseThrow();

        // Convertir a DTO y retornar
        return CoordinadorMapper.toDTO(coordinador);
    }

    @Override
    @Transactional(readOnly = true) // Importante: mantiene la sesión de Hibernate abierta durante la ejecución del
                                    // método
    public Page<CoordinadorDTO> obtenerTodosLosCoordinadoresPaginado(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        // Usar el método que ordena alfabéticamente por nombre y apellido
        Page<User> coordinadoresPage = userRepository.findAllByRoleOrderedByNombreApellido(Role.COORDINADOR, pageable);

        // Convertir a DTOs de forma segura sin intentar inicializar colecciones lazy
        List<CoordinadorDTO> dtoList = new ArrayList<>();

        for (User coordinador : coordinadoresPage.getContent()) {
            CoordinadorDTO dto = new CoordinadorDTO();
            dto.setId(coordinador.getId());
            dto.setUsername(coordinador.getUsername());
            dto.setNombre(coordinador.getNombre());
            dto.setApellido(coordinador.getApellido());
            dto.setDni(coordinador.getDni());
            dto.setEmail(coordinador.getEmail());
            dto.setTelefono(coordinador.getTelefono());

            // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la
            // relación sede
            String nombreSede = coordinador.getSedeNombre();
            if (nombreSede == null && coordinador.getSede() != null) {
                nombreSede = coordinador.getSede().getNombre();
            }
            dto.setSede(nombreSede);

            // Obtener los asesores de forma segura para cada coordinador y ordenarlos
            // alfabéticamente
            List<AsesorDTO> asesoresDTO = userRepository.findByCoordinadorId(coordinador.getId()).stream()
                    .filter(user -> user.getRole() == Role.ASESOR)
                    .map(AsesorMapper::toDTO)
                    // Ordenar alfabéticamente por nombre y apellido
                    .sorted(java.util.Comparator.comparing(AsesorDTO::getNombre)
                            .thenComparing(AsesorDTO::getApellido))
                    .collect(Collectors.toList());

            dto.setAsesores(asesoresDTO);
            dtoList.add(dto);
        }

        return new PageImpl<>(dtoList, pageable, coordinadoresPage.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public CoordinadorDTO obtenerCoordinadorPorId(Long coordinadorId) {
        User coordinador = userRepository.findById(coordinadorId)
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado"));

        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario no es un coordinador");
        }

        // Crear un DTO directamente sin intentar inicializar la colección de asesores
        CoordinadorDTO dto = new CoordinadorDTO();
        dto.setId(coordinador.getId());
        dto.setUsername(coordinador.getUsername());
        dto.setNombre(coordinador.getNombre());
        dto.setApellido(coordinador.getApellido());
        dto.setDni(coordinador.getDni());
        dto.setEmail(coordinador.getEmail());
        dto.setTelefono(coordinador.getTelefono());

        // Obtener el nombre de la sede, primero desde sedeNombre y si es null, desde la
        // relación sede
        String nombreSede = coordinador.getSedeNombre();
        if (nombreSede == null && coordinador.getSede() != null) {
            nombreSede = coordinador.getSede().getNombre();
        }
        dto.setSede(nombreSede);

        // Obtener los asesores de forma segura y ordenarlos alfabéticamente
        List<AsesorDTO> asesoresDTO = userRepository.findByCoordinadorId(coordinadorId).stream()
                .filter(user -> user.getRole() == Role.ASESOR)
                .map(AsesorMapper::toDTO)
                // Ordenar alfabéticamente por nombre y apellido
                .sorted(java.util.Comparator.comparing(AsesorDTO::getNombre)
                        .thenComparing(AsesorDTO::getApellido))
                .collect(Collectors.toList());

        dto.setAsesores(asesoresDTO);

        return dto;
    }

    @Override
    @Transactional(readOnly = true)
    public List<AsesorDTO> obtenerAsesoresSinCoordinador() {
        // Obtener asesores sin coordinador
        List<User> asesoresSinCoordinador = userRepository.findByRoleAndCoordinadorIsNull(Role.ASESOR);

        // Para cada asesor, asegurarse de que la sede se cargue correctamente y ordenar
        // alfabéticamente
        return asesoresSinCoordinador.stream()
                .map(asesor -> {
                    // Inicializar la sede si es necesario (esto fuerza la carga de la relación)
                    if (asesor.getSede() != null) {
                        asesor.getSede().getNombre(); // Forzar la inicialización
                    }
                    return AsesorMapper.toDTO(asesor);
                })
                // Ordenar alfabéticamente por nombre y apellido
                .sorted(java.util.Comparator.comparing(AsesorDTO::getNombre)
                        .thenComparing(AsesorDTO::getApellido))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean eliminarAsesorDeCoordinador(Long coordinadorId, Long asesorId) {
        // Verificar que el coordinador existe
        if (!userRepository.existsById(coordinadorId)) {
            throw new RuntimeException("Coordinador no encontrado");
        }

        User asesor = userRepository.findById(asesorId)
                .orElseThrow(() -> new RuntimeException("Asesor no encontrado"));

        if (asesor.getCoordinador() != null && asesor.getCoordinador().getId().equals(coordinadorId)) {
            asesor.setCoordinador(null);
            userRepository.save(asesor);
            return true;
        }

        return false;
    }

    /**
     * Método para obtener, de forma paginada, los clientes residenciales de los
     * asesores asignados a un coordinador.
     * Se filtra por fecha (formato "yyyy-MM-dd"); si no se envía, se usa la fecha
     * actual.
     *
     * La respuesta se estructura en un GenericResponse con:
     * - rpta: 1
     * - msg: "Clientes con usuario obtenidos correctamente"
     * - data: { totalItems, totalPages, currentPage, clientes: [...] }
     *
     * @param coordinadorId ID del coordinador
     * @param fecha         Fecha en formato "yyyy-MM-dd" (opcional)
     * @param page          Número de página
     * @param size          Tamaño de página
     * @return GenericResponse con la información paginada de clientes
     */
    @Override
    public GenericResponse<Map<String, Object>> obtenerClientesPorCoordinador(
            Long coordinadorId,
            String dni,
            String nombre,
            String numeroMovil,
            String fecha,
            int page,
            int size) {
        // Validar que el coordinador existe y es de rol COORDINADOR
        User coordinador = userRepository.findById(coordinadorId)
                .orElseThrow(() -> new RuntimeException("Coordinador no encontrado con ID: " + coordinadorId));

        if (coordinador.getRole() != Role.COORDINADOR) {
            throw new RuntimeException("El usuario con ID: " + coordinadorId + " no tiene rol de coordinador");
        }

        // Parsear la fecha si está presente
        LocalDate filtroFecha = null;
        if (fecha != null && !fecha.isEmpty()) {
            try {
                filtroFecha = LocalDate.parse(fecha, DateTimeFormatter.ISO_DATE);
            } catch (DateTimeParseException e) {
                throw new RuntimeException("Formato de fecha inválido. Use: yyyy-MM-dd");
            }
        }

        // Preparar paginación
        Pageable pageable = PageRequest.of(page, size);

        // Aplicar todos los filtros disponibles en el repositorio
        Page<ClienteConUsuarioDTO> pageClientes = coordinadorClienteRepository.findClientesByCoordinadorWithFilters(
                coordinadorId,
                dni,
                nombre,
                numeroMovil,
                filtroFecha,
                pageable);

        // Preparar respuesta con metadatos de paginación
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("clientes", pageClientes.getContent());
        responseData.put("totalItems", pageClientes.getTotalElements());
        responseData.put("totalPages", pageClientes.getTotalPages());
        responseData.put("currentPage", pageClientes.getNumber());
        responseData.put("size", pageClientes.getSize());
        responseData.put("hasNext", pageClientes.hasNext());
        responseData.put("hasPrevious", pageClientes.hasPrevious());

        return new GenericResponse<>(1, "Clientes obtenidos correctamente", responseData);
    }

}
