package com.midas.crm.service;

import com.midas.crm.entity.Role;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.utils.validation.ExcelUserValidation;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Service
public class ExcelService {

    @Autowired
    private SedeRepository sedeRepository;

    public List<User> leerUsuariosDesdeExcel(MultipartFile file, Role role) throws IOException {
        List<User> usuarios = new ArrayList<>();
        List<String> errores = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            // Saltar la primera fila (encabezados)
            if (rows.hasNext()) {
                rows.next();
            }

            int rowNumber = 2; // Comenzamos desde la fila 2 (después del encabezado)
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                User user = new User();
                user.setFechaCreacion(LocalDateTime.now());
                user.setRole(role);
                user.setEstado("A");

                try {
                    // Leer cada celda según su posición
                    user.setUsername(getCellValueAsString(currentRow.getCell(0)));
                    user.setPassword(getCellValueAsString(currentRow.getCell(1)));
                    user.setDni(getCellValueAsString(currentRow.getCell(2)));
                    user.setNombre(getCellValueAsString(currentRow.getCell(3)));
                    user.setApellido(getCellValueAsString(currentRow.getCell(4)));

                    // Obtener y validar la sede
                    String sedeIdStr = getCellValueAsString(currentRow.getCell(5));
                    try {
                        Long sedeId = Long.parseLong(sedeIdStr);
                        Sede sede = sedeRepository.findById(sedeId)
                                .orElseThrow(() -> new RuntimeException("Sede no encontrada con ID: " + sedeId));
                        user.setSede(sede);
                    } catch (NumberFormatException e) {
                        errores.add("Fila " + rowNumber + ": El ID de la sede debe ser un número válido");
                        continue;
                    }

                    user.setEmail(getCellValueAsString(currentRow.getCell(6)));
                    user.setTelefono(getCellValueAsString(currentRow.getCell(7)));

                    // Validar el usuario
                    List<String> erroresUsuario = ExcelUserValidation.validateExcelUser(user, rowNumber);
                    if (!erroresUsuario.isEmpty()) {
                        errores.addAll(erroresUsuario);
                    } else {
                        usuarios.add(user);
                    }
                } catch (Exception e) {
                    errores.add("Fila " + rowNumber + ": Error al procesar la fila - " + e.getMessage());
                }
                rowNumber++;
            }
        }

        if (!errores.isEmpty()) {
            throw new RuntimeException("Errores en el archivo Excel:\n" + String.join("\n", errores));
        }

        return usuarios;
    }

    public List<User> leerUsuariosDesdeExcelBackoffice(MultipartFile file) throws IOException {
        List<User> usuarios = new ArrayList<>();
        List<String> errores = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.iterator();

            // Saltar la primera fila (encabezados)
            if (rows.hasNext()) {
                rows.next();
            }

            int rowNumber = 2; // Comenzamos desde la fila 2 (después del encabezado)
            while (rows.hasNext()) {
                Row currentRow = rows.next();
                User user = new User();
                user.setFechaCreacion(LocalDateTime.now());
                user.setRole(Role.BACKOFFICE);
                user.setEstado("A");

                try {
                    // Leer cada celda según su posición
                    user.setUsername(getCellValueAsString(currentRow.getCell(0)));
                    user.setPassword(getCellValueAsString(currentRow.getCell(1)));
                    user.setDni(getCellValueAsString(currentRow.getCell(2)));
                    user.setNombre(getCellValueAsString(currentRow.getCell(3)));
                    user.setApellido(getCellValueAsString(currentRow.getCell(4)));
                    user.setSedeNombre(getCellValueAsString(currentRow.getCell(5)));
                    user.setEmail(getCellValueAsString(currentRow.getCell(6)));
                    user.setTelefono(getCellValueAsString(currentRow.getCell(7)));

                    // Validar el usuario
                    List<String> erroresUsuario = ExcelUserValidation.validateExcelUser(user, rowNumber);
                    if (!erroresUsuario.isEmpty()) {
                        errores.addAll(erroresUsuario);
                    } else {
                        usuarios.add(user);
                    }
                } catch (Exception e) {
                    errores.add("Fila " + rowNumber + ": Error al procesar la fila - " + e.getMessage());
                }
                rowNumber++;
            }
        }

        if (!errores.isEmpty()) {
            throw new RuntimeException("Errores en el archivo Excel:\n" + String.join("\n", errores));
        }

        return usuarios;
    }

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
