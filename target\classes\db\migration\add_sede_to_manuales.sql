-- Migración para agregar soporte de sede a la tabla manuales
-- Fecha: 2024
-- Descripción: Agrega la columna sede_id a la tabla manuales para permitir 
--              filtrar manuales por sede específica

-- Agregar la columna sede_id como foreign key opcional
ALTER TABLE manuales 
ADD COLUMN sede_id BIGINT NULL;

-- Agregar la foreign key constraint
ALTER TABLE manuales 
ADD CONSTRAINT fk_manuales_sede 
FOREIGN KEY (sede_id) REFERENCES sedes(id) 
ON DELETE SET NULL 
ON UPDATE CASCADE;

-- Crear índice para mejorar el rendimiento de las consultas por sede
CREATE INDEX idx_manuales_sede_id ON manuales(sede_id);

-- Crear índice compuesto para consultas frecuentes
CREATE INDEX idx_manuales_sede_active ON manuales(sede_id, is_active);

-- Comentarios para documentar los cambios
COMMENT ON COLUMN manuales.sede_id IS 'ID de la sede a la que pertenece el manual. NULL significa que es un manual global para todas las sedes';
