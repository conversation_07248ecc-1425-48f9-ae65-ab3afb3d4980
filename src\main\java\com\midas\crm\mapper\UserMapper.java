package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.user.*;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import org.hibernate.Hibernate;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Mapeador de entidades <-> DTOs de User.
 *  – Centraliza la lógica de obtención de nombre/ID de sede para evitar duplicación.
 *  – Evita NPE usando Objects.requireNonNullElse.
 *  – Mantiene los comentarios en español como solicitaste.
 */
public final class UserMapper {

    /** Evita instanciación */
    private UserMapper() {}

    // ---------- CREACIÓN ----------

    public static User toEntity(UserCreateDTO dto) {
        User user = new User();
        user.setUsername(dto.getUsername());
        user.setPassword(dto.getPassword());           // Se encripta en el servicio
        user.setNombre(dto.getNombre());
        user.setApellido(dto.getApellido());
        user.setDni(dto.getDni());
        user.setTelefono(dto.getTelefono());
        user.setEmail(dto.getEmail());
        user.setRole(dto.getRole() != null ? dto.getRole() : Role.ASESOR);
        user.setEstado("A");
        user.setFechaCreacion(LocalDateTime.now());

        if (dto.getSede_id() != null) {
            Sede sede = new Sede();          // constructor vacío
            sede.setId(dto.getSede_id());    // le inyectamos solo el ID
            user.setSede(sede);
        }

        // --- Coordinador (opcional) ---
        if (dto.getCoordinador_id() != null) {
            user.setCoordinador(new User(dto.getCoordinador_id()));
        }

        return user;
    }

    // ---------- RESPUESTA / LISTADOS ----------

    public static UserResponseDTO toResponseDTO(User user) {
        if (user == null) return null;

        return new UserResponseDTO(
                user.getId(),
                user.getUsername(),
                user.getNombre(),
                user.getApellido(),
                user.getDni(),
                user.getTelefono(),
                user.getEmail(),
                user.getFechaCreacion(),
                user.getFechaCese(),
                user.getEstado(),
                user.getRole(),
                resolveSedeNombre(user),
                resolveSedeId(user),
                user.getCoordinador() != null ? toDTO(user.getCoordinador()) : null
        );
    }

    public static UserDTO toDTO(User user) {
        // Crear el DTO del coordinador si existe
        UserCoordinadorDTO coordinadorDTO = null;
        if (user.getCoordinador() != null) {
            User coordinador = user.getCoordinador();
            coordinadorDTO = new UserCoordinadorDTO(
                    coordinador.getId(),
                    coordinador.getUsername(),
                    coordinador.getNombre(),
                    coordinador.getApellido(),
                    coordinador.getDni(),
                    coordinador.getTelefono(),
                    coordinador.getEmail(),
                    resolveSedeNombre(coordinador),
                    resolveSedeId(coordinador)
            );
        }

        return new UserDTO(
                user.getId(),
                user.getUsername(),
                user.getNombre(),
                user.getApellido(),
                user.getDni(),
                user.getTelefono(),
                user.getEmail(),
                user.getFechaCreacion(),
                user.getFechaCese(),
                user.getEstado(),
                user.getRole(),
                resolveSedeNombre(user),
                resolveSedeId(user),
                coordinadorDTO
        );
    }

    public static UserItemDTO toItemDTO(User user) {
        return new UserItemDTO(
                user.getId(),
                user.getUsername(),
                user.getNombre(),
                user.getApellido(),
                user.getDni(),
                user.getTelefono(),
                user.getEmail(),
                user.getFechaCreacion(),
                user.getFechaCese(),
                user.getEstado(),
                user.getRole(),
                resolveSedeNombre(user),
                resolveSedeId(user)
        );
    }

    // ---------- ACTUALIZACIÓN ----------

    public static UserUpdateDTO toUpdateDTO(User user) {
        UserUpdateDTO dto = new UserUpdateDTO();
        dto.setUsername(user.getUsername());
        dto.setNombre(user.getNombre());
        dto.setApellido(user.getApellido());
        dto.setTelefono(user.getTelefono());
        dto.setEmail(user.getEmail());
        dto.setEstado(user.getEstado());
        dto.setSede(resolveSedeNombre(user));          // nombre legible
        dto.setSede_id(resolveSedeId(user));           // y el ID
        // La contraseña se omite por seguridad
        return dto;
    }

    public static void updateUserFromDTO(User user, UserUpdateDTO dto) {

        // ----- Campos simples -----
        if (dto.getUsername() != null)   user.setUsername(dto.getUsername());
        if (dto.getNombre() != null)     user.setNombre(dto.getNombre());
        if (dto.getApellido() != null)   user.setApellido(dto.getApellido());
        if (dto.getTelefono() != null)   user.setTelefono(dto.getTelefono());
        if (dto.getEmail() != null)      user.setEmail(dto.getEmail());
        if (dto.getEstado() != null)     user.setEstado(dto.getEstado());

        // ----- SEDE -----
        if (dto.getSede_id() != null) {              // Prioridad al ID
            user.setSede(new Sede(dto.getSede_id()));/* ← usa el constructor corto  */
            // No limpiamos el sedeNombre para mantener compatibilidad
        } else if (dto.getSede() != null            // Si no llegó ID pero sí nombre
                && !dto.getSede().isBlank()) {
            user.setSede(null);                      // Evita FK inconsistente
            user.setSedeNombre(dto.getSede());
        }

        // ----- ROLE -----
        if (dto.getRole() != null) {
            user.setRole(dto.getRole());
        }

        // ----- COORDINADOR -----
        if (dto.getCoordinador_id() != null) {
            user.setCoordinador(new User(dto.getCoordinador_id()));
        } else if (dto.isRemoveCoordinador()) {
            user.setCoordinador(null);
        }

        // La contraseña se maneja en el servicio para poder encriptarla
    }


    // ---------- PRIVADOS ----------

    /* ----------------------------------------------------------------------
     Devuelve el nombre de la sede sin provocar LazyInitializationException
     – Primero intenta obtener el nombre desde la relación LAZY si está inicializada
     – Si no está disponible, usa el backup `sedeNombre`
     – Si hay un ID de sede pero no hay nombre, devuelve "Sede #ID" como fallback
  ---------------------------------------------------------------------- */
    private static String resolveSedeNombre(User user) {
        // 1) Relación LAZY si está inicializada (prioridad)
        if (user.getSede() != null && org.hibernate.Hibernate.isInitialized(user.getSede()) && user.getSede().getNombre() != null) {
            return user.getSede().getNombre();
        }

        // 2) Backup grabado en la entidad
        if (user.getSedeNombre() != null && !user.getSedeNombre().isBlank()) {
            return user.getSedeNombre();
        }

        // 3) Fallback basado en ID si está disponible
        if (user.getSede() != null && user.getSede().getId() != null) {
            return "Sede #" + user.getSede().getId();
        }

        // 4) Sin datos
        return "";
    }



    private static Long resolveSedeId(User user) {
        return user.getSede() != null ? user.getSede().getId() : null;
    }
}
