package com.midas.crm.controller;

import com.midas.crm.entity.DTO.route.RouteCreateDTO;
import com.midas.crm.entity.DTO.route.RouteDTO;
import com.midas.crm.entity.DTO.route.RoutePageDTO;
import com.midas.crm.entity.DTO.route.RouteUpdateDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.service.RouteService;
import com.midas.crm.utils.GenericResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("${api.route.routes}")
@PreAuthorize("hasRole('ADMIN') or hasRole('PROGRAMADOR')")
public class RouteController {

    private final RouteService routeService;

    /**
     * Lista todas las rutas con paginación
     */
    @GetMapping("/listar")
    public ResponseEntity<GenericResponse<RoutePageDTO>> listRoutes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        return routeService.listRoutes(page, size, activeOnly);
    }

    /**
     * Busca rutas por query con paginación
     */
    @GetMapping("/buscar")
    public ResponseEntity<GenericResponse<RoutePageDTO>> searchRoutes(
            @RequestParam(required = false) String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "true") boolean activeOnly) {
        return routeService.searchRoutes(query, page, size, activeOnly);
    }

    /**
     * Crea una nueva ruta
     */
    @PostMapping("/crear")
    public ResponseEntity<GenericResponse<RouteDTO>> createRoute(@Valid @RequestBody RouteCreateDTO createDTO) {
        return routeService.createRoute(createDTO);
    }

    /**
     * Actualiza una ruta existente
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<RouteDTO>> updateRoute(
            @PathVariable Long id,
            @Valid @RequestBody RouteUpdateDTO updateDTO) {
        return routeService.updateRoute(id, updateDTO);
    }

    /**
     * Elimina una ruta (soft delete)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteRoute(@PathVariable Long id) {
        return routeService.deleteRoute(id);
    }

    /**
     * Obtiene una ruta por ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<RouteDTO>> getRouteById(@PathVariable Long id) {
        return routeService.getRouteById(id);
    }

    /**
     * Obtiene una ruta por path
     */
    @GetMapping("/path")
    public ResponseEntity<GenericResponse<RouteDTO>> getRouteByPath(@RequestParam String path) {
        return routeService.getRouteByPath(path);
    }

    /**
     * Obtiene rutas por rol
     */
    @GetMapping("/por-rol/{role}")
    public ResponseEntity<GenericResponse<List<RouteDTO>>> getRoutesByRole(@PathVariable String role) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return routeService.getRoutesByRole(roleEnum);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Asigna un rol a una ruta
     */
    @PostMapping("/{routeId}/roles/{role}")
    public ResponseEntity<GenericResponse<Void>> assignRoleToRoute(
            @PathVariable Long routeId,
            @PathVariable String role,
            @RequestParam(defaultValue = "true") Boolean canAccess) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return routeService.assignRoleToRoute(routeId, roleEnum, canAccess);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Remueve un rol de una ruta
     */
    @DeleteMapping("/{routeId}/roles/{role}")
    public ResponseEntity<GenericResponse<Void>> removeRoleFromRoute(
            @PathVariable Long routeId,
            @PathVariable String role) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return routeService.removeRoleFromRoute(routeId, roleEnum);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Obtiene roles por ruta
     */
    @GetMapping("/{routeId}/roles")
    public ResponseEntity<GenericResponse<List<Role>>> getRolesByRoute(@PathVariable Long routeId) {
        return routeService.getRolesByRoute(routeId);
    }

    /**
     * Verifica si existe una ruta con el path dado
     */
    @GetMapping("/exists")
    public ResponseEntity<GenericResponse<Boolean>> existsByPath(@RequestParam String path) {
        boolean exists = routeService.existsByPath(path);
        return ResponseEntity.ok(new GenericResponse<>(1, "Verificación completada", exists));
    }

    /**
     * Importa múltiples rutas desde Angular
     */
    @PostMapping("/importar-desde-angular")
    public ResponseEntity<GenericResponse<String>> importFromAngular(@RequestBody List<RouteCreateDTO> routes) {
        try {
            int imported = 0;
            int skipped = 0;

            for (RouteCreateDTO routeData : routes) {
                if (!routeService.existsByPath(routeData.getPath())) {
                    routeService.createRoute(routeData);
                    imported++;
                } else {
                    skipped++;
                }
            }

            String message = String.format("Importación completada: %d rutas importadas, %d omitidas (ya existían)",
                    imported, skipped);
            return ResponseEntity.ok(new GenericResponse<>(1, message, message));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(0, "Error al importar rutas: " + e.getMessage(), null));
        }
    }
}
