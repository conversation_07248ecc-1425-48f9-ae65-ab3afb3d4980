package com.midas.crm.entity.DTO.user;

import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserItemDTO {
    private Long id;
    private String username;
    private String nombre;
    private String apellido;
    private String dni;
    private String telefono;
    private String email;
    private LocalDateTime fechaCreacion;
    private LocalDate fechaCese;
    private String estado;
    private Role role;
    private String sede; // Este es el campo sedeNombre
    private Long sede_id; // Cambiado de sedeId a sede_id para coincidir con el frontend
}