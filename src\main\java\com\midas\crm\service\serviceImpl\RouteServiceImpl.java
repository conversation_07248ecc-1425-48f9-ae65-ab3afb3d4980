package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.route.RouteCreateDTO;
import com.midas.crm.entity.DTO.route.RouteDTO;
import com.midas.crm.entity.DTO.route.RoutePageDTO;
import com.midas.crm.entity.DTO.route.RouteUpdateDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.RoleRoute;
import com.midas.crm.entity.Route;
import com.midas.crm.repository.RoleRouteRepository;
import com.midas.crm.repository.RouteRepository;
import com.midas.crm.service.RouteService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.ResponseBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class RouteServiceImpl implements RouteService {

    private final RouteRepository routeRepository;
    private final RoleRouteRepository roleRouteRepository;

    @Override
    public ResponseEntity<GenericResponse<RoutePageDTO>> listRoutes(int page, int size, boolean activeOnly) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
            Page<Route> routePage;

            if (activeOnly) {
                routePage = routeRepository.findByIsActiveTrue(pageable);
            } else {
                routePage = routeRepository.findAll(pageable);
            }

            List<RouteDTO> routeDTOs = routePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            RoutePageDTO pageDTO = new RoutePageDTO(
                    routeDTOs,
                    routePage.getTotalElements(),
                    routePage.getTotalPages(),
                    page,
                    size);

            return ResponseBuilder.success(pageDTO, "Rutas obtenidas exitosamente");
        } catch (Exception e) {
            log.error("Error al listar rutas: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener las rutas: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<RoutePageDTO>> searchRoutes(String query, int page, int size,
                                                                      boolean activeOnly) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("name").ascending());
            Page<Route> routePage;

            if (query == null || query.trim().isEmpty()) {
                return listRoutes(page, size, activeOnly);
            }

            if (activeOnly) {
                routePage = routeRepository.findByQueryAndIsActiveTrue(query.trim(), pageable);
            } else {
                routePage = routeRepository.findByQuery(query.trim(), pageable);
            }

            List<RouteDTO> routeDTOs = routePage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            RoutePageDTO pageDTO = new RoutePageDTO(
                    routeDTOs,
                    routePage.getTotalElements(),
                    routePage.getTotalPages(),
                    page,
                    size);

            return ResponseBuilder.success(pageDTO, "Búsqueda de rutas completada exitosamente");
        } catch (Exception e) {
            log.error("Error al buscar rutas: {}", e.getMessage());
            return ResponseBuilder.error("Error al buscar rutas: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<RouteDTO>> createRoute(RouteCreateDTO createDTO) {
        try {
            // Verificar si ya existe una ruta con el mismo path
            if (routeRepository.existsByPath(createDTO.getPath())) {
                return ResponseBuilder.error("Ya existe una ruta con el path: " + createDTO.getPath());
            }

            Route route = new Route();
            route.setPath(createDTO.getPath());
            route.setName(createDTO.getName());
            route.setDescription(createDTO.getDescription());
            route.setComponentName(createDTO.getComponentName());
            route.setModulePath(createDTO.getModulePath());
            route.setIsActive(createDTO.getIsActive());
            route.setRequiresAuth(createDTO.getRequiresAuth());

            Route savedRoute = routeRepository.save(route);

            // Asignar roles si se proporcionaron
            if (createDTO.getAllowedRoles() != null && !createDTO.getAllowedRoles().isEmpty()) {
                for (Role role : createDTO.getAllowedRoles()) {
                    RoleRoute roleRoute = new RoleRoute();
                    roleRoute.setRole(role);
                    roleRoute.setRoute(savedRoute);
                    roleRoute.setCanAccess(true);
                    roleRouteRepository.save(roleRoute);
                }
            }

            RouteDTO routeDTO = convertToDTO(savedRoute);
            return ResponseBuilder.created(routeDTO, "Ruta creada exitosamente");
        } catch (Exception e) {
            log.error("Error al crear ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al crear la ruta: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<RouteDTO>> updateRoute(Long id, RouteUpdateDTO updateDTO) {
        try {
            Optional<Route> routeOpt = routeRepository.findById(id);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            Route route = routeOpt.get();

            if (updateDTO.getName() != null) {
                route.setName(updateDTO.getName());
            }
            if (updateDTO.getDescription() != null) {
                route.setDescription(updateDTO.getDescription());
            }
            if (updateDTO.getComponentName() != null) {
                route.setComponentName(updateDTO.getComponentName());
            }
            if (updateDTO.getModulePath() != null) {
                route.setModulePath(updateDTO.getModulePath());
            }
            if (updateDTO.getIsActive() != null) {
                route.setIsActive(updateDTO.getIsActive());
            }
            if (updateDTO.getRequiresAuth() != null) {
                route.setRequiresAuth(updateDTO.getRequiresAuth());
            }

            Route savedRoute = routeRepository.save(route);

            // Actualizar roles si se proporcionaron
            if (updateDTO.getAllowedRoles() != null) {
                // Eliminar roles existentes
                List<RoleRoute> existingRoleRoutes = roleRouteRepository.findByRoute(route);
                roleRouteRepository.deleteAll(existingRoleRoutes);

                // Agregar nuevos roles
                for (Role role : updateDTO.getAllowedRoles()) {
                    RoleRoute roleRoute = new RoleRoute();
                    roleRoute.setRole(role);
                    roleRoute.setRoute(savedRoute);
                    roleRoute.setCanAccess(true);
                    roleRouteRepository.save(roleRoute);
                }
            }

            RouteDTO routeDTO = convertToDTO(savedRoute);
            return ResponseBuilder.success(routeDTO, "Ruta actualizada exitosamente");
        } catch (Exception e) {
            log.error("Error al actualizar ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al actualizar la ruta: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<Void>> deleteRoute(Long id) {
        try {
            Optional<Route> routeOpt = routeRepository.findById(id);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            Route route = routeOpt.get();
            route.setIsActive(false); // Soft delete
            routeRepository.save(route);

            return ResponseBuilder.success(null, "Ruta eliminada exitosamente");
        } catch (Exception e) {
            log.error("Error al eliminar ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al eliminar la ruta: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<RouteDTO>> getRouteById(Long id) {
        try {
            Optional<Route> routeOpt = routeRepository.findById(id);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            RouteDTO routeDTO = convertToDTO(routeOpt.get());
            return ResponseBuilder.success(routeDTO, "Ruta obtenida exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener la ruta: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<RouteDTO>> getRouteByPath(String path) {
        try {
            Optional<Route> routeOpt = routeRepository.findByPath(path);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            RouteDTO routeDTO = convertToDTO(routeOpt.get());
            return ResponseBuilder.success(routeDTO, "Ruta obtenida exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener ruta por path: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener la ruta: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<List<RouteDTO>>> getRoutesByRole(Role role) {
        try {
            List<RoleRoute> roleRoutes = roleRouteRepository.findAccessibleRoutesByRole(role);
            List<RouteDTO> routeDTOs = roleRoutes.stream()
                    .map(rr -> convertToDTO(rr.getRoute()))
                    .collect(Collectors.toList());

            return ResponseBuilder.success(routeDTOs, "Rutas obtenidas exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener rutas por rol: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener rutas por rol: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<Void>> assignRoleToRoute(Long routeId, Role role, Boolean canAccess) {
        try {
            Optional<Route> routeOpt = routeRepository.findById(routeId);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            Route route = routeOpt.get();
            Optional<RoleRoute> existingRoleRoute = roleRouteRepository.findByRoleAndRoute(role, route);

            if (existingRoleRoute.isPresent()) {
                // Actualizar el acceso existente
                RoleRoute roleRoute = existingRoleRoute.get();
                roleRoute.setCanAccess(canAccess);
                roleRouteRepository.save(roleRoute);
            } else {
                // Crear nueva relación
                RoleRoute roleRoute = new RoleRoute();
                roleRoute.setRole(role);
                roleRoute.setRoute(route);
                roleRoute.setCanAccess(canAccess);
                roleRouteRepository.save(roleRoute);
            }

            return ResponseBuilder.success(null, "Rol asignado a la ruta exitosamente");
        } catch (Exception e) {
            log.error("Error al asignar rol a ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al asignar rol a ruta: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public ResponseEntity<GenericResponse<Void>> removeRoleFromRoute(Long routeId, Role role) {
        try {
            Optional<Route> routeOpt = routeRepository.findById(routeId);
            if (routeOpt.isEmpty()) {
                return ResponseBuilder.notFound("Ruta no encontrada");
            }

            Route route = routeOpt.get();
            roleRouteRepository.deleteByRoleAndRoute(role, route);

            return ResponseBuilder.success(null, "Rol removido de la ruta exitosamente");
        } catch (Exception e) {
            log.error("Error al remover rol de ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al remover rol de ruta: " + e.getMessage());
        }
    }

    @Override
    public ResponseEntity<GenericResponse<List<Role>>> getRolesByRoute(Long routeId) {
        try {
            List<RoleRoute> roleRoutes = roleRouteRepository.findAccessibleRolesByRouteId(routeId);
            List<Role> roles = roleRoutes.stream()
                    .map(RoleRoute::getRole)
                    .collect(Collectors.toList());

            return ResponseBuilder.success(roles, "Roles obtenidos exitosamente");
        } catch (Exception e) {
            log.error("Error al obtener roles por ruta: {}", e.getMessage());
            return ResponseBuilder.error("Error al obtener roles por ruta: " + e.getMessage());
        }
    }

    @Override
    public Route findRouteById(Long id) {
        return routeRepository.findById(id).orElse(null);
    }

    @Override
    public Route findRouteByPath(String path) {
        return routeRepository.findByPath(path).orElse(null);
    }

    @Override
    public boolean existsByPath(String path) {
        return routeRepository.existsByPath(path);
    }

    private RouteDTO convertToDTO(Route route) {
        RouteDTO dto = new RouteDTO();
        dto.setId(route.getId());
        dto.setPath(route.getPath());
        dto.setName(route.getName());
        dto.setDescription(route.getDescription());
        dto.setComponentName(route.getComponentName());
        dto.setModulePath(route.getModulePath());
        dto.setIsActive(route.getIsActive());
        dto.setRequiresAuth(route.getRequiresAuth());
        dto.setCreatedAt(route.getCreatedAt());
        dto.setUpdatedAt(route.getUpdatedAt());

        // Obtener roles permitidos
        if (route.getRoleRoutes() != null) {
            List<Role> allowedRoles = route.getRoleRoutes().stream()
                    .filter(rr -> rr.getCanAccess())
                    .map(RoleRoute::getRole)
                    .collect(Collectors.toList());
            dto.setAllowedRoles(allowedRoles);
        }

        return dto;
    }
}
