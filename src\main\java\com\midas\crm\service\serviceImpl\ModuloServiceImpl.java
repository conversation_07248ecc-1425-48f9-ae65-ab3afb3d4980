package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.DTO.modulo.ModuloCreateDTO;
import com.midas.crm.entity.DTO.modulo.ModuloDTO;
import com.midas.crm.entity.DTO.modulo.ModuloUpdateDTO;
import com.midas.crm.entity.Modulo;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.ModuloMapper;
import com.midas.crm.repository.CursoRepository;
import com.midas.crm.repository.ModuloRepository;
import com.midas.crm.service.ModuloService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ModuloServiceImpl implements ModuloService {

    private final ModuloRepository moduloRepository;
    private final CursoRepository cursoRepository;

    @Override
    @Transactional
    public ModuloDTO createModulo(ModuloCreateDTO dto) {
        // Verificar si ya existe un módulo con el mismo título en el mismo curso
        if (moduloRepository.existsByTituloAndCursoId(dto.getTitulo(), dto.getCursoId())) {
            throw new MidasExceptions(MidasErrorMessage.MODULO_ALREADY_EXISTS);
        }

        // Obtener el curso
        Curso curso = cursoRepository.findById(dto.getCursoId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        // Crear el módulo
        Modulo modulo = ModuloMapper.toEntity(dto, curso);

        return ModuloMapper.toDTO(moduloRepository.save(modulo));
    }

    @Override
    @Transactional(readOnly = true)
    public List<ModuloDTO> listModulos() {
        return moduloRepository.findAll().stream()
                .map(ModuloMapper::toDTOWithoutSecciones)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ModuloDTO> listModulosByCursoId(Long cursoId) {
        return moduloRepository.findByCursoIdOrderByOrdenAsc(cursoId).stream()
                .map(ModuloMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public ModuloDTO getModuloById(Long id) {
        return moduloRepository.findById(id)
                .map(ModuloMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.MODULO_NOT_FOUND));
    }

    @Override
    @Transactional
    public ModuloDTO updateModulo(Long id, ModuloUpdateDTO dto) {
        Modulo modulo = moduloRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.MODULO_NOT_FOUND));

        ModuloMapper.updateEntity(modulo, dto);

        return ModuloMapper.toDTO(moduloRepository.save(modulo));
    }

    @Override
    @Transactional
    public void deleteModulo(Long id) {
        if (!moduloRepository.existsById(id)) {
            throw new MidasExceptions(MidasErrorMessage.MODULO_NOT_FOUND);
        }

        moduloRepository.deleteById(id);
    }
}
