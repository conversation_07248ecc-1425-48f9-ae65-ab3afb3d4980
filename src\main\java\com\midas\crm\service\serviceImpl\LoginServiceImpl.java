package com.midas.crm.service.serviceImpl;


import com.midas.crm.entity.DTO.websocket.UserStatusDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;

import com.midas.crm.service.AuthenticationService;
import com.midas.crm.service.LoginService;
import com.midas.crm.service.UserConnectionService;
import com.midas.crm.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementación del servicio de login
 * Centraliza toda la lógica de inicio de sesión para mejorar el rendimiento y mantenibilidad
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LoginServiceImpl implements LoginService {

    private final UserService userService;
    private final AuthenticationService authenticationService;
    private final UserConnectionService userConnectionService;

    @Override
    public Map<String, Object> login(User user) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> responseData = new HashMap<>();

        try {
            // 1. Autenticación y generación del token
            long authStartTime = System.currentTimeMillis();
            User authenticatedUser = authenticationService.signInAndReturnJWT(user);
            long authTime = System.currentTimeMillis() - authStartTime;

            // 2. Registrar la conexión del usuario
            long connectStartTime = System.currentTimeMillis();
            UserStatusDTO userStatus = userConnectionService.connectUser(authenticatedUser.getId());
            long connectTime = System.currentTimeMillis() - connectStartTime;

            // 3. Preparar datos de respuesta
            // Datos básicos que siempre se incluyen
            responseData.put("token", authenticatedUser.getToken());
            responseData.put("status", userStatus.getStatus());
            responseData.put("userId", authenticatedUser.getId());
            responseData.put("username", authenticatedUser.getUsername());
            responseData.put("nombre", authenticatedUser.getNombre());
            responseData.put("apellido", authenticatedUser.getApellido());
            responseData.put("role", authenticatedUser.getRole());

            if (authenticatedUser.getSede() != null) {
                responseData.put("sede_id", authenticatedUser.getSede().getId());
                responseData.put("sede", authenticatedUser.getSede().getNombre());
            }

            // 4. Cargar datos adicionales según el rol del usuario
            long relationStartTime = System.currentTimeMillis();
            loadAdditionalDataByRole(authenticatedUser, responseData);
            long relationTime = System.currentTimeMillis() - relationStartTime;

            // 5. Registrar métricas de rendimiento
            long totalTime = System.currentTimeMillis() - startTime;
            // Incluir métricas en modo desarrollo
            Map<String, Object> metrics = new HashMap<>();
            metrics.put("authTime", authTime);
            metrics.put("connectTime", connectTime);
            metrics.put("relationTime", relationTime);
            metrics.put("totalTime", totalTime);
            responseData.put("_metrics", metrics);

            return responseData;
        } catch (Exception e) {

            throw e;
        }
    }

    /**
     * Carga datos adicionales según el rol del usuario
     * @param authenticatedUser Usuario autenticado
     * @param responseData Mapa de datos de respuesta
     */
    private void loadAdditionalDataByRole(User authenticatedUser, Map<String, Object> responseData) {
        try {
            // COORDINADOR: listar asesores
            if (authenticatedUser.getRole() == Role.COORDINADOR) {
                // Obtener los asesores del coordinador desde la base de datos
                User coordinadorCompleto = userService.findUserById(authenticatedUser.getId());

                // Usar Optional, Stream y lambdas para procesar la lista de asesores
                List<Map<String, Object>> asesoresData = Optional.ofNullable(coordinadorCompleto)
                    .map(User::getAsesores)
                    .filter(asesores -> !asesores.isEmpty())
                    .map(asesores -> asesores.stream()
                        .map(asesor -> {
                            Map<String, Object> asesorData = new HashMap<>();
                            asesorData.put("id", asesor.getId());
                            asesorData.put("username", asesor.getUsername());
                            asesorData.put("nombre", asesor.getNombre());
                            asesorData.put("apellido", asesor.getApellido());
                            asesorData.put("dni", asesor.getDni());
                            asesorData.put("telefono", asesor.getTelefono());
                            asesorData.put("email", asesor.getEmail());
                            return asesorData;
                        })
                        .collect(Collectors.toList()))
                    .orElse(new ArrayList<>());

                responseData.put("asesores", asesoresData);
            }
            // ASESOR: mostrar su coordinador asignado
            else if (authenticatedUser.getRole() == Role.ASESOR) {
                // Usar Optional y lambdas para obtener y procesar el coordinador
                Map<String, Object> coordinadorData = Optional.ofNullable(userService.findUserById(authenticatedUser.getId()))
                    .map(User::getCoordinador)
                    .map(coordinador -> {
                        try {
                            // Obtener el coordinador directamente de la base de datos para evitar problemas de proxy
                            return userService.findUserById(coordinador.getId());
                        } catch (Exception e) {
                            return null;
                        }
                    })
                    .map(coordinador -> {
                        Map<String, Object> data = new HashMap<>();
                        data.put("id", coordinador.getId());
                        data.put("username", coordinador.getUsername());
                        data.put("nombre", coordinador.getNombre());
                        data.put("apellido", coordinador.getApellido());
                        data.put("dni", coordinador.getDni());
                        data.put("telefono", coordinador.getTelefono());
                        data.put("email", coordinador.getEmail());
                        return data;
                    })
                    .orElse(null);

                responseData.put("coordinador", coordinadorData);
            }
        } catch (Exception e) {
            // Si hay un error al cargar las relaciones, continuamos
        }
    }

    @Override
    public boolean userExists(String username) {
        // Verificar si el usuario existe y está activo usando programación funcional
        return userService.findByUsername(username)
            .filter(user -> "A".equals(user.getEstado()))
            .isPresent();
    }
}
