package com.midas.crm.repository;

import com.midas.crm.entity.DetalleRespuestaUsuario;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.RespuestaUsuario;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DetalleRespuestaUsuarioRepository extends JpaRepository<DetalleRespuestaUsuario, Long> {
    List<DetalleRespuestaUsuario> findByRespuestaUsuario(RespuestaUsuario respuestaUsuario);
    List<DetalleRespuestaUsuario> findByRespuestaUsuarioId(Long respuestaUsuarioId);
    Optional<DetalleRespuestaUsuario> findByRespuestaUsuarioAndPregunta(RespuestaUsuario respuestaUsuario, Pregunta pregunta);
    Optional<DetalleRespuestaUsuario> findByRespuestaUsuarioIdAndPreguntaId(Long respuestaUsuarioId, Long preguntaId);
    int countByRespuestaUsuarioIdAndEsCorrectaTrue(Long respuestaUsuarioId);
}
