package com.midas.crm.event;

import com.midas.crm.entity.Anuncio;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se actualiza un anuncio
 */
@Getter
public class AnuncioUpdatedEvent extends ApplicationEvent {
    
    private final Anuncio anuncio;
    
    public AnuncioUpdatedEvent(Object source, Anuncio anuncio) {
        super(source);
        this.anuncio = anuncio;
    }
}
