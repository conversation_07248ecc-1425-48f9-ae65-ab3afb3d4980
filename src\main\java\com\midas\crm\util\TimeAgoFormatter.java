package com.midas.crm.util;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Utilidad para formatear fechas en formato "hace X tiempo"
 */
public class TimeAgoFormatter {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");

    /**
     * Formatea una fecha en formato "hace X tiempo"
     * @param dateTime La fecha a formatear
     * @return String con el formato "hace X tiempo"
     */
    public static String format(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }

        LocalDateTime now = LocalDateTime.now();
        Duration duration = Duration.between(dateTime, now);

        // Si es en el futuro (por si hay algún error de sincronización)
        if (duration.isNegative()) {
            return "ahora";
        }

        long seconds = duration.getSeconds();
        
        // Menos de un minuto
        if (seconds < 60) {
            return "hace un momento";
        }
        
        // Menos de una hora
        if (seconds < 3600) {
            long minutes = seconds / 60;
            return "hace " + minutes + (minutes == 1 ? " minuto" : " minutos");
        }
        
        // Menos de un día
        if (seconds < 86400) {
            long hours = seconds / 3600;
            return "hace " + hours + (hours == 1 ? " hora" : " horas");
        }
        
        // Menos de una semana
        if (seconds < 604800) {
            long days = seconds / 86400;
            return "hace " + days + (days == 1 ? " día" : " días");
        }
        
        // Menos de un mes (aproximadamente)
        if (seconds < 2592000) {
            long weeks = seconds / 604800;
            return "hace " + weeks + (weeks == 1 ? " semana" : " semanas");
        }
        
        // Menos de un año
        if (seconds < 31536000) {
            long months = seconds / 2592000;
            return "hace " + months + (months == 1 ? " mes" : " meses");
        }
        
        // Más de un año
        long years = seconds / 31536000;
        return "hace " + years + (years == 1 ? " año" : " años");
    }

    /**
     * Formatea una fecha en formato corto (solo fecha)
     * @param dateTime La fecha a formatear
     * @return String con el formato "dd/MM/yyyy"
     */
    public static String formatShortDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATE_FORMATTER);
    }

    /**
     * Formatea una fecha en formato corto (solo hora)
     * @param dateTime La fecha a formatear
     * @return String con el formato "HH:mm"
     */
    public static String formatShortTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(TIME_FORMATTER);
    }

    /**
     * Formatea una fecha en formato completo
     * @param dateTime La fecha a formatear
     * @return String con el formato "dd/MM/yyyy HH:mm"
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }
}
