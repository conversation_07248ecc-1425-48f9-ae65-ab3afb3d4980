package com.midas.crm.entity.DTO.encuesta;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO para transferir información de detalles de respuestas de usuario a preguntas de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaEncuestaUsuarioDTO {
    private Long id;
    private Long respuestaEncuestaUsuarioId;
    private Long preguntaId;
    private String preguntaEnunciado;
    private String preguntaTipo;
    private Long opcionId;
    private String opcionTexto;
    private String respuestaTexto;
    private Double respuestaNumero;
    private LocalDateTime respuestaFecha;
    private LocalDateTime fechaCreacion;
    private LocalDateTime fechaActualizacion;
}
