package com.midas.crm.service;

/**
 * Servicio para monitorear y gestionar conexiones
 * Proporciona métodos para monitorear y limpiar conexiones de base de datos y WebSocket
 */
public interface ConnectionMonitorService {

    /**
     * Monitorea el estado actual de las conexiones
     * @return Resumen del estado de las conexiones
     */
    String monitorConnections();

    /**
     * Limpia las conexiones inactivas o problemáticas
     * @return Número de conexiones limpiadas
     */
    int cleanupConnections();

    /**
     * Obtiene estadísticas detalladas sobre las conexiones
     * @return Estadísticas de conexiones
     */
    String getConnectionStats();

    /**
     * Fuerza una limpieza agresiva de todas las conexiones
     * @return Resultado de la operación
     */
    String forceCleanup();
}
