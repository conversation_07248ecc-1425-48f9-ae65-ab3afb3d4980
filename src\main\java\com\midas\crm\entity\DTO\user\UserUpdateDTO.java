package com.midas.crm.entity.DTO.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.midas.crm.entity.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserUpdateDTO {
    private String username;
    private String nombre;
    private String apellido;
    private String telefono;
    private String email;
    private String estado;
    private String sede; // Este es el campo sedeNombre (mantenerlo por compatibilidad)
    private String password;
    private Long sede_id; // Nuevo campo para el ID de la sede
    private Long coordinador_id; // ID del coordinador
    private boolean removeCoordinador; // Indica si se debe eliminar el coordinador
    private Role role; // Rol del usuario

    // Método para manejar el caso en que sede venga como objeto
    public void setSede(Object sedeObj) {
        if (sedeObj == null) {
            this.sede = null;
        } else if (sedeObj instanceof String) {
            this.sede = (String) sedeObj;
        } else if (sedeObj instanceof Map) {
            // Si es un objeto JSON (Map), extraemos el ID si existe
            Map<?, ?> sedeMap = (Map<?, ?>) sedeObj;
            if (sedeMap.containsKey("id")) {
                Object idObj = sedeMap.get("id");
                if (idObj instanceof Number) {
                    this.sede_id = ((Number) idObj).longValue();
                } else if (idObj instanceof String) {
                    try {
                        this.sede_id = Long.parseLong((String) idObj);
                    } catch (NumberFormatException e) {
                        // Si no se puede convertir a Long, ignoramos
                    }
                }
            }
            // Dejamos sede como null para que sede_id maneje la relación
            this.sede = null;
        } else {
            // Para cualquier otro tipo de objeto, lo ignoramos
            this.sede = null;
        }
    }
}