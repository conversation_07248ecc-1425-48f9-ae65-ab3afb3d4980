package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.cliente.ClienteConUsuarioDTO;
import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.ClienteResidencialMapper;
import com.midas.crm.repository.ClienteResidencialRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.ClienteResidencialExcelService;
import com.midas.crm.service.ClienteResidencialService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import org.hibernate.LazyInitializationException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ClienteResidencialServiceImpl implements ClienteResidencialService {

    private final ClienteResidencialRepository clienteRepo;
    private final UserRepository userRepo;
    private final ClienteResidencialExcelService clienteResidencialExcelService;

    @Override
    public List<ClienteResidencial> listarTodos() {
        return clienteRepo.findAll();
    }

    @Override
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuario(int page, int size) {
        Pageable paging = PageRequest.of(page, size);
        Page<ClienteConUsuarioDTO> pageClientes = clienteRepo.obtenerClientesConUsuario(paging);

        Map<String, Object> response = new HashMap<>();
        response.put("clientes", pageClientes.getContent());
        response.put("currentPage", pageClientes.getNumber());
        response.put("totalItems", pageClientes.getTotalElements());
        response.put("totalPages", pageClientes.getTotalPages());

        return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                "Clientes con usuario obtenidos correctamente", response));
    }

    @Override
    public List<ClienteResidencial> buscarPorMovil(String movil) {
        return clienteRepo.findByMovilContacto(movil);
    }

    @Override
    public ClienteResidencial obtenerPorId(Long id) {
        return clienteRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Cliente no encontrado con id: " + id));
    }

    @Transactional
    @Override
    public ClienteResidencial guardar(ClienteResidencial cliente, Long usuarioId) {
        if (cliente == null || usuarioId == null) {
            throw new IllegalArgumentException("Cliente y usuarioId no pueden ser nulos");
        }

        // Optimización: Usar getOne/getReference en lugar de findById para cargar solo
        // la referencia
        // sin necesidad de hacer un SELECT completo
        User usuario = userRepo.getReferenceById(usuarioId);

        // Establecer valores necesarios
        cliente.setUsuario(usuario);
        cliente.setFechaCreacion(LocalDateTime.now());

        // Inicializar colecciones si son nulas para evitar problemas de serialización
        if (cliente.getMovilesAPortar() == null) {
            cliente.setMovilesAPortar(new ArrayList<>());
        }

        // Guardar el cliente con flush para asegurar que se persista inmediatamente
        return clienteRepo.saveAndFlush(cliente);
    }

    @Override
    public ClienteResidencial actualizar(Long id, ClienteResidencial cliente) {
        // Buscar el cliente existente en la base de datos
        ClienteResidencial clienteExistente = clienteRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Cliente no encontrado con id: " + id));

        // Copiar las propiedades no nulas del objeto 'cliente' al objeto
        // 'clienteExistente'
        BeanUtils.copyProperties(cliente, clienteExistente, getNullPropertyNames(cliente));

        // Guardar y retornar el cliente actualizado
        return clienteRepo.save(clienteExistente);
    }

    @Override
    @Transactional
    public ClienteResidencial actualizarNotaAgenteComparadorIA(Long id, BigDecimal nota) {
        // Buscar el cliente existente en la base de datos
        ClienteResidencial clienteExistente = clienteRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Cliente no encontrado con id: " + id));

        // Actualizar solo la nota del agente comparador IA
        clienteExistente.setNotaAgenteComparadorIA(nota);

        // Guardar y retornar el cliente actualizado
        return clienteRepo.save(clienteExistente);
    }

    public String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        // Siempre ignoramos el campo "id" para que no se modifique
        emptyNames.add("id");
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    @Override
    public void eliminar(Long id) {
        if (!clienteRepo.existsById(id)) {
            throw new NoSuchElementException("Cliente no encontrado con id: " + id);
        }
        clienteRepo.deleteById(id);
    }

    @Override
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltrados(String dniAsesor,
            String nombreAsesor, String numeroMovil, String fecha, int page, int size) {

        // Convertir la cadena de fecha a LocalDate si se envía
        LocalDate fechaLocalDate = null;
        if (fecha != null && !fecha.isEmpty()) {
            try {
                fechaLocalDate = LocalDate.parse(fecha);
            } catch (Exception e) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(GenericResponseConstants.ERROR, "Formato de fecha inválido", null));
            }
        }

        // Preparar el nombre del asesor para la búsqueda
        String nombreAsesorFiltro = nombreAsesor;
        if (nombreAsesor != null && !nombreAsesor.trim().isEmpty()) {
            // Asegurarse de que el nombre del asesor esté correctamente formateado para la
            // búsqueda
            nombreAsesorFiltro = nombreAsesor.trim();
        }

        Pageable paging = PageRequest.of(page, size);
        Page<ClienteConUsuarioDTO> pageClientes = clienteRepo.obtenerClientesConUsuarioFiltrados(
                dniAsesor, nombreAsesorFiltro, numeroMovil, fechaLocalDate, paging);

        Map<String, Object> response = new HashMap<>();
        response.put("clientes", pageClientes.getContent());
        response.put("currentPage", pageClientes.getNumber());
        response.put("totalItems", pageClientes.getTotalElements());
        response.put("totalPages", pageClientes.getTotalPages());

        return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                "Clientes filtrados obtenidos correctamente", response));
    }

    @Override
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltradosPorFechaActual(
            String dniAsesor, String nombreAsesor, String numeroMovil, int page, int size) {

        // Preparar el nombre del asesor para la búsqueda
        String nombreAsesorFiltro = nombreAsesor;
        if (nombreAsesor != null && !nombreAsesor.trim().isEmpty()) {
            // Asegurarse de que el nombre del asesor esté correctamente formateado para la
            // búsqueda
            nombreAsesorFiltro = nombreAsesor.trim();
        }

        Pageable paging = PageRequest.of(page, size);
        Page<ClienteConUsuarioDTO> pageClientes = clienteRepo.obtenerClientesConUsuarioFiltradosPorFechaActual(
                dniAsesor, nombreAsesorFiltro, numeroMovil, paging);

        Map<String, Object> response = new HashMap<>();
        response.put("clientes", pageClientes.getContent());
        response.put("currentPage", pageClientes.getNumber());
        response.put("totalItems", pageClientes.getTotalElements());
        response.put("totalPages", pageClientes.getTotalPages());

        return ResponseEntity.ok(new GenericResponse<>(GenericResponseConstants.SUCCESS,
                "Clientes filtrados por fecha actual obtenidos correctamente", response));
    }

    @Override
    public List<ClienteResidencialDTO> getClientesByAsesorId(Long asesorId) {
        List<ClienteResidencial> clientes = clienteRepo.findByUsuarioId(asesorId);
        return clientes.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Long countClientesByAsesorId(Long asesorId) {
        return clienteRepo.countClientesByUsuarioId(asesorId);
    }

    @Override
    public List<ClienteResidencialDTO> getVentasRealizadasByAsesorId(Long asesorId) {
        List<ClienteResidencial> ventas = clienteRepo.findVentasRealizadasByUsuarioId(asesorId);
        return ventas.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ClienteResidencialDTO convertToDTO(ClienteResidencial cliente) {
        return ClienteResidencialMapper.toDTO(cliente);
    }

    /**
     * Reemplaza el método buscarPorDniMovilYFechaEntre en tu
     * ClienteResidencialServiceImpl
     */
    @Override
    @Transactional(readOnly = true) // Mantiene la sesión de Hibernate abierta durante la ejecución del método
    public List<ClienteResidencial> buscarPorDniMovilYFechaEntre(String dni, String nombreCompleto, String movil,
            LocalDateTime inicio, LocalDateTime fin) {
        List<ClienteResidencial> clientes = clienteRepo.buscarPorDniMovilYFechaEntre(dni, nombreCompleto, movil, inicio,
                fin);

        // Inicializar explícitamente las colecciones lazy para cada cliente
        for (ClienteResidencial cliente : clientes) {
            // Forzar la inicialización de la colección movilesAPortar
            if (cliente.getMovilesAPortar() == null) {
                cliente.setMovilesAPortar(new ArrayList<>());
            } else {
                // Crear una nueva lista para evitar problemas de serialización
                List<String> movilesAPortar = new ArrayList<>(cliente.getMovilesAPortar());
                cliente.setMovilesAPortar(movilesAPortar);
            }

            // Asegurarse de que el campo numeroMoviles esté inicializado
            if (cliente.getNumeroMoviles() == null) {
                cliente.setNumeroMoviles("");
            }
        }

        return clientes;
    }

    @Override
    public ResponseEntity<byte[]> exportarExcel() {
        try {
            byte[] excelData = clienteResidencialExcelService.generarExcelClientesMasivo();

            if (excelData == null || excelData.length == 0) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=clientes_residenciales_masivo.xlsx");
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            if (!(e instanceof MidasExceptions)) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true) // Mantener la sesión de Hibernate abierta durante toda la operación
    public ResponseEntity<byte[]> exportarExcelPorMovil(String movil) {
        try {
            // Generar el Excel dentro de una transacción para mantener la sesión abierta
            byte[] excelData = clienteResidencialExcelService.generarExcelClienteIndividual(movil);

            if (excelData == null || excelData.length == 0) {
                throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_NOT_FOUND);
            }

            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=cliente_residencial_" + movil + ".xlsx");
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // Configurar headers adicionales para mejorar el rendimiento
            headers.set(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.set(HttpHeaders.PRAGMA, "no-cache");
            headers.set(HttpHeaders.EXPIRES, "0");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            if (e instanceof LazyInitializationException) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL,
                        "Error al cargar datos relacionados. Por favor, inténtelo de nuevo.");
            }
            if (!(e instanceof MidasExceptions)) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }
            throw e;
        }
    }

    @Override
    public ResponseEntity<byte[]> exportarExcelPorFecha(String fechaStr) {
        LocalDate fecha;
        try {
            // Se espera que la fecha esté en formato "yyyy-MM-dd"
            fecha = LocalDate.parse(fechaStr, DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (DateTimeParseException e) {
            throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_INVALID_DATA);
        }

        try {
            byte[] excelData = clienteResidencialExcelService.generarExcelClientesPorFecha(fecha);
            if (excelData == null || excelData.length == 0) {
                throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_NOT_FOUND);
            }

            // Para el nombre del archivo usamos el mismo formato ISO (por ejemplo,
            // "2025-03-13")
            String fechaArchivo = fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=clientes_residenciales_" + fechaArchivo + ".xlsx");
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            if (!(e instanceof MidasExceptions)) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }
            throw e;
        }
    }

    @Override
    public ResponseEntity<byte[]> exportarExcelPorRangoFechas(String fechaInicioStr, String fechaFinStr) {
        LocalDate fechaInicio, fechaFin;
        try {
            // Se espera que las fechas estén en formato "yyyy-MM-dd"
            fechaInicio = LocalDate.parse(fechaInicioStr, DateTimeFormatter.ISO_LOCAL_DATE);
            fechaFin = LocalDate.parse(fechaFinStr, DateTimeFormatter.ISO_LOCAL_DATE);

            // Validar que la fecha de inicio no sea posterior a la fecha de fin
            if (fechaInicio.isAfter(fechaFin)) {
                throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_INVALID_DATA);
            }
        } catch (DateTimeParseException e) {
            throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_INVALID_DATA);
        }

        try {
            // Convertir las fechas a LocalDateTime para incluir todo el día
            LocalDateTime fechaInicioDateTime = fechaInicio.atStartOfDay();
            LocalDateTime fechaFinDateTime = fechaFin.atTime(LocalTime.MAX);

            byte[] excelData = clienteResidencialExcelService.generarExcelClientesPorRangoFechas(
                    fechaInicioDateTime, fechaFinDateTime);

            if (excelData == null || excelData.length == 0) {
                throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_NOT_FOUND);
            }

            // Para el nombre del archivo usamos el formato ISO con ambas fechas
            String nombreArchivo = "clientes_residenciales_" +
                    fechaInicio.format(DateTimeFormatter.ISO_LOCAL_DATE) +
                    "_a_" +
                    fechaFin.format(DateTimeFormatter.ISO_LOCAL_DATE) +
                    ".xlsx";

            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + nombreArchivo);
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            return new ResponseEntity<>(excelData, headers, HttpStatus.OK);
        } catch (Exception e) {
            if (!(e instanceof MidasExceptions)) {
                throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
            }
            throw e;
        }
    }

}
