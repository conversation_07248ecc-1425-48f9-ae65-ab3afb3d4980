package com.midas.crm.service;

import com.midas.crm.entity.User;
import java.util.Map;

/**
 * Servicio dedicado para el proceso de inicio de sesión
 * Separa la lógica de autenticación del controlador para mejorar la organización y rendimiento
 */
public interface LoginService {

    /**
     * Realiza el proceso completo de inicio de sesión
     * @param user Usuario con credenciales para iniciar sesión
     * @return Mapa con los datos de respuesta (token, estado, etc.)
     */
    Map<String, Object> login(User user);
    
    /**
     * Verifica si un usuario existe por su nombre de usuario
     * @param username Nombre de usuario a verificar
     * @return true si el usuario existe, false en caso contrario
     */
    boolean userExists(String username);
}
