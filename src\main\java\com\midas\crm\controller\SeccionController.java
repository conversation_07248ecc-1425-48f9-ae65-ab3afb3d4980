package com.midas.crm.controller;

import com.midas.crm.entity.DTO.seccion.SeccionCreateDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionUpdateDTO;
import com.midas.crm.service.SeccionService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.secciones}")
@RequiredArgsConstructor
public class SeccionController {

    private final SeccionService seccionService;

    /**
     * Crea una nueva sección
     */
    @PostMapping
    public ResponseEntity<GenericResponse<SeccionDTO>> createSeccion(@Valid @RequestBody SeccionCreateDTO dto) {
        return Optional.ofNullable(dto)
                .map(seccionService::createSeccion)
                .map(seccion -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Sección creada exitosamente", seccion)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las secciones
     */
    @GetMapping
    public ResponseEntity<GenericResponse<List<SeccionDTO>>> getAllSecciones() {
        List<SeccionDTO> secciones = seccionService.listSecciones();
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Secciones obtenidas exitosamente", secciones)
        );
    }

    /**
     * Obtiene una sección por su ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<SeccionDTO>> getSeccionById(@PathVariable Long id) {
        SeccionDTO seccion = seccionService.getSeccionById(id);
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Sección obtenida exitosamente", seccion)
        );
    }

    /**
     * Obtiene todas las secciones de un módulo
     */
    @GetMapping("/modulo/{moduloId}")
    public ResponseEntity<GenericResponse<List<SeccionDTO>>> getSeccionesByModuloId(@PathVariable Long moduloId) {
        List<SeccionDTO> secciones = seccionService.getSeccionesByModuloId(moduloId);
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Secciones obtenidas exitosamente", secciones)
        );
    }

    /**
     * Actualiza una sección existente
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<SeccionDTO>> updateSeccion(
            @PathVariable Long id,
            @Valid @RequestBody SeccionUpdateDTO dto) {
        return Optional.ofNullable(dto)
                .map(updateDto -> seccionService.updateSeccion(id, updateDto))
                .map(seccion -> ResponseEntity.ok(
                        new GenericResponse<>(GenericResponseConstants.SUCCESS, "Sección actualizada exitosamente", seccion)
                ))
                .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una sección
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deleteSeccion(@PathVariable Long id) {
        seccionService.deleteSeccion(id);
        return ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Sección eliminada exitosamente", null)
        );
    }
}
