package com.midas.crm.mapper;

import com.midas.crm.entity.Curso;
import com.midas.crm.entity.DTO.modulo.ModuloCreateDTO;
import com.midas.crm.entity.DTO.modulo.ModuloDTO;
import com.midas.crm.entity.DTO.modulo.ModuloUpdateDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import com.midas.crm.entity.Modulo;

import java.util.List;
import java.util.stream.Collectors;

public final class ModuloMapper {

    private ModuloMapper() {}

    public static Modulo toEntity(ModuloCreateDTO dto, Curso curso) {
        Modulo modulo = new Modulo();
        modulo.setTitulo(dto.getTitulo());
        modulo.setDescripcion(dto.getDescripcion());
        modulo.setOrden(dto.getOrden());
        modulo.setCurso(curso);
        modulo.setEstado("A");
        return modulo;
    }

    public static ModuloDTO toDTO(Modulo modulo) {
        if (modulo == null) return null;

        List<SeccionDTO> seccionesDTO = null;
        if (modulo.getSecciones() != null && !modulo.getSecciones().isEmpty()) {
            seccionesDTO = modulo.getSecciones().stream()
                    .map(SeccionMapper::toDTO)
                    .collect(Collectors.toList());
        }

        return new ModuloDTO(
                modulo.getId(),
                modulo.getTitulo(),
                modulo.getDescripcion(),
                modulo.getOrden(),
                modulo.getCurso() != null ? modulo.getCurso().getId() : null,
                modulo.getCurso() != null ? modulo.getCurso().getNombre() : null,
                modulo.getEstado(),
                modulo.getFechaCreacion(),
                seccionesDTO
        );
    }

    public static ModuloDTO toDTOWithoutSecciones(Modulo modulo) {
        if (modulo == null) return null;

        return new ModuloDTO(
                modulo.getId(),
                modulo.getTitulo(),
                modulo.getDescripcion(),
                modulo.getOrden(),
                modulo.getCurso() != null ? modulo.getCurso().getId() : null,
                modulo.getCurso() != null ? modulo.getCurso().getNombre() : null,
                modulo.getEstado(),
                modulo.getFechaCreacion(),
                null
        );
    }

    public static void updateEntity(Modulo modulo, ModuloUpdateDTO dto) {
        if (dto.getTitulo() != null) modulo.setTitulo(dto.getTitulo());
        if (dto.getDescripcion() != null) modulo.setDescripcion(dto.getDescripcion());
        if (dto.getOrden() != null) modulo.setOrden(dto.getOrden());
        if (dto.getEstado() != null) modulo.setEstado(dto.getEstado());
    }
}
