package com.midas.crm.entity.DTO.cuestionario;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DetalleRespuestaUsuarioCreateDTO {
    @NotNull(message = "El ID de la respuesta del usuario es obligatorio")
    private Long respuestaUsuarioId;
    
    @NotNull(message = "El ID de la pregunta es obligatorio")
    private Long preguntaId;
    
    private Long respuestaId;
    private String textoRespuesta;
}
