package com.midas.crm.mapper;

import com.midas.crm.entity.Calendar;
import com.midas.crm.entity.DTO.calendar.CalendarDTO;
import com.midas.crm.entity.DTO.calendar.CalendarResponseDTO;
import com.midas.crm.entity.User;
import com.midas.crm.repository.UserRepository;

public class CalendarMapper {

    public static Calendar toEntity(CalendarDTO dto, UserRepository userRepository) {
        Calendar calendar = new Calendar();
        mapDtoToEntity(dto, calendar, userRepository);
        return calendar;
    }

    public static void mapDtoToEntity(CalendarDTO dto, Calendar calendar, UserRepository userRepository) {
        calendar.setTitulo(dto.getTitulo());
        calendar.setDescripcion(dto.getDescripcion());
        calendar.setColor(dto.getColor());
        calendar.setFechaInicio(dto.getFechaInicio());
        calendar.setHoraInicio(dto.getHoraInicio());
        calendar.setFechaFinal(dto.getFechaFinal());
        calendar.setHoraFinal(dto.getHoraFinal());
        calendar.setIsActive(true);
        calendar.setIsSeen(false);
        calendar.setDeleted(false);

        if (dto.getUserCreateId() != null) {
            User userCreate = userRepository.findById(dto.getUserCreateId()).orElse(null);
            calendar.setUserCreate(userCreate);
        }

        if (dto.getUserUpdateId() != null) {
            User userUpdate = userRepository.findById(dto.getUserUpdateId()).orElse(null);
            calendar.setUserUpdate(userUpdate);
        }

        if (dto.getUserDeleteId() != null) {
            User userDelete = userRepository.findById(dto.getUserDeleteId()).orElse(null);
            calendar.setUserDelete(userDelete);
        }
    }

    public static CalendarResponseDTO toResponseDTO(Calendar calendar) {
        CalendarResponseDTO dto = new CalendarResponseDTO();
        dto.setId(calendar.getId());
        dto.setTitulo(calendar.getTitulo());
        dto.setDescripcion(calendar.getDescripcion());
        dto.setColor(calendar.getColor());
        dto.setFechaInicio(calendar.getFechaInicio());
        dto.setHoraInicio(calendar.getHoraInicio());
        dto.setFechaFinal(calendar.getFechaFinal());
        dto.setHoraFinal(calendar.getHoraFinal());
        dto.setIsActive(calendar.getIsActive());
        dto.setIsSeen(calendar.getIsSeen());
        dto.setUserCreateId(calendar.getUserCreate() != null ? calendar.getUserCreate().getId() : null);
        return dto;
    }
}
