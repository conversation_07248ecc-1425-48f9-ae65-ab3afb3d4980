package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.progreso.ProgresoCreateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUpdateDTO;
import com.midas.crm.entity.DTO.progreso.ProgresoUsuarioDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.ProgresoUsuario;
import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.ProgresoUsuarioMapper;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.repository.ProgresoUsuarioRepository;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.ProgresoUsuarioService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ProgresoUsuarioServiceImpl implements ProgresoUsuarioService {

    private final ProgresoUsuarioRepository progresoUsuarioRepository;
    private final LeccionRepository leccionRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public ProgresoUsuarioDTO createProgreso(ProgresoCreateDTO dto) {
        // Obtener la lección
        Leccion leccion = leccionRepository.findById(dto.getLeccionId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Obtener el usuario
        User usuario = userRepository.findById(dto.getUsuarioId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Verificar si ya existe un progreso para esta lección y usuario
        Optional<ProgresoUsuario> progresoExistente = progresoUsuarioRepository.findByUsuarioAndLeccion(usuario, leccion);
        
        if (progresoExistente.isPresent()) {
            // Actualizar el progreso existente
            ProgresoUsuario progreso = progresoExistente.get();
            if (dto.getCompletado() != null) progreso.setCompletado(dto.getCompletado());
            if (dto.getSegundosVistos() != null) progreso.setSegundosVistos(dto.getSegundosVistos());
            if (dto.getPorcentajeCompletado() != null) progreso.setPorcentajeCompletado(dto.getPorcentajeCompletado());
            if (dto.getUltimaPosicion() != null) progreso.setUltimaPosicion(dto.getUltimaPosicion());
            
            return ProgresoUsuarioMapper.toDTO(progresoUsuarioRepository.save(progreso));
        } else {
            // Crear un nuevo progreso
            ProgresoUsuario progreso = ProgresoUsuarioMapper.toEntity(dto, usuario, leccion);
            return ProgresoUsuarioMapper.toDTO(progresoUsuarioRepository.save(progreso));
        }
    }

    @Override
    @Transactional
    public ProgresoUsuarioDTO updateProgreso(Long leccionId, Long usuarioId, ProgresoUpdateDTO dto) {
        // Obtener la lección
        Leccion leccion = leccionRepository.findById(leccionId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Obtener el usuario
        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Obtener el progreso
        ProgresoUsuario progreso = progresoUsuarioRepository.findByUsuarioAndLeccion(usuario, leccion)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PROGRESO_NOT_FOUND));

        // Actualizar el progreso
        ProgresoUsuarioMapper.updateEntity(progreso, dto);
        
        return ProgresoUsuarioMapper.toDTO(progresoUsuarioRepository.save(progreso));
    }

    @Override
    @Transactional(readOnly = true)
    public ProgresoUsuarioDTO getProgresoByLeccionAndUsuario(Long leccionId, Long usuarioId) {
        // Obtener la lección
        Leccion leccion = leccionRepository.findById(leccionId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Obtener el usuario
        User usuario = userRepository.findById(usuarioId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Obtener el progreso
        return progresoUsuarioRepository.findByUsuarioAndLeccion(usuario, leccion)
                .map(ProgresoUsuarioMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.PROGRESO_NOT_FOUND));
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProgresoUsuarioDTO> getProgresoByUsuario(Long usuarioId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        return progresoUsuarioRepository.findByUsuarioId(usuarioId).stream()
                .map(ProgresoUsuarioMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProgresoUsuarioDTO> getProgresoByCursoAndUsuario(Long cursoId, Long usuarioId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        return progresoUsuarioRepository.findByCursoIdAndUsuarioId(cursoId, usuarioId).stream()
                .map(ProgresoUsuarioMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getResumenProgresoCurso(Long cursoId, Long usuarioId) {
        // Verificar que el usuario existe
        if (!userRepository.existsById(usuarioId)) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND);
        }

        // Obtener el número total de lecciones del curso
        Long totalLecciones = progresoUsuarioRepository.countTotalLeccionesByCurso(cursoId);
        
        // Obtener el número de lecciones completadas por el usuario
        Long leccionesCompletadas = progresoUsuarioRepository.countCompletedLeccionesByCursoAndUsuario(cursoId, usuarioId);
        
        // Calcular el porcentaje de progreso
        int porcentajeProgreso = totalLecciones > 0 ? (int) ((leccionesCompletadas * 100) / totalLecciones) : 0;
        
        // Crear el mapa de respuesta
        Map<String, Object> resumen = new HashMap<>();
        resumen.put("cursoId", cursoId);
        resumen.put("usuarioId", usuarioId);
        resumen.put("totalLecciones", totalLecciones);
        resumen.put("leccionesCompletadas", leccionesCompletadas);
        resumen.put("porcentajeProgreso", porcentajeProgreso);
        
        return resumen;
    }
}
