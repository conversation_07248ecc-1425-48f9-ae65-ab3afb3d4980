package com.midas.crm.controller;

import com.midas.crm.entity.DTO.role.RoleDTO;
import com.midas.crm.entity.DTO.role.RoleRouteDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.service.RoleService;
import com.midas.crm.utils.GenericResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("${api.route.roles}")
@PreAuthorize("hasRole('ADMIN') or hasRole('PROGRAMADOR')")
public class RoleController {

    private final RoleService roleService;

    /**
     * Obtiene todos los roles disponibles
     */
    @GetMapping("/listar")
    public ResponseEntity<GenericResponse<List<RoleDTO>>> getAllRoles() {
        return roleService.getAllRoles();
    }

    /**
     * Obtiene información de un rol específico
     */
    @GetMapping("/{role}")
    public ResponseEntity<GenericResponse<RoleDTO>> getRoleInfo(@PathVariable String role) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return roleService.getRoleInfo(roleEnum);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Obtiene las rutas asignadas a un rol
     */
    @GetMapping("/{role}/rutas")
    public ResponseEntity<GenericResponse<List<RoleRouteDTO>>> getRoleRoutes(@PathVariable String role) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return roleService.getRoleRoutes(roleEnum);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Actualiza las rutas asignadas a un rol
     */
    @PutMapping("/{role}/rutas")
    public ResponseEntity<GenericResponse<Void>> updateRoleRoutes(
            @PathVariable String role,
            @RequestBody List<Long> routeIds) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            return roleService.updateRoleRoutes(roleEnum, routeIds);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * Obtiene roles que tienen acceso a una ruta específica
     */
    @GetMapping("/por-ruta")
    public ResponseEntity<GenericResponse<List<Role>>> getRolesByRoutePath(@RequestParam String path) {
        return roleService.getRolesByRoutePath(path);
    }

    /**
     * Verifica si un rol tiene acceso a una ruta específica
     */
    @GetMapping("/{role}/acceso")
    public ResponseEntity<GenericResponse<Boolean>> hasRoleAccessToRoute(
            @PathVariable String role,
            @RequestParam String path) {
        try {
            Role roleEnum = Role.valueOf(role.toUpperCase());
            boolean hasAccess = roleService.hasRoleAccessToRoute(roleEnum, path);
            return ResponseEntity.ok(new GenericResponse<>(1, "Verificación completada", hasAccess));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
