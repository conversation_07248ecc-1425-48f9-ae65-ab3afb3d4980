package com.midas.crm.controller;

import com.google.api.client.auth.oauth2.Credential;
import com.midas.crm.service.GoogleOAuthService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Controlador para manejar el callback de OAuth2 de Google Drive
 * Este endpoint es llamado por Google después de la autorización
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class OAuth2CallbackController {

    private final GoogleOAuthService googleOAuthService;

    /**
     * Endpoint para manejar el callback de OAuth2 de Google Drive
     * URL: https://apisozarusac.com/BackendJava/oauth2/callback
     */
    @GetMapping("/oauth2/callback")
    public ResponseEntity<String> handleOAuth2Callback(
            @RequestParam(value = "code", required = false) String authorizationCode,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "error", required = false) String error,
            HttpServletRequest request,
            HttpServletResponse response) throws IOException {
        
        log.info("Recibido callback de OAuth2 - Code: {}, State: {}, Error: {}", 
                authorizationCode != null ? "presente" : "ausente", state, error);
        
        if (error != null) {
            log.error("Error en OAuth2 callback: {}", error);
            return ResponseEntity.badRequest()
                    .body("Error en la autorización: " + error);
        }
        
        if (authorizationCode == null) {
            log.error("Código de autorización no recibido");
            return ResponseEntity.badRequest()
                    .body("Código de autorización no recibido");
        }
        
        try {
            // Procesar el código de autorización
            Credential credential = googleOAuthService.processAuthorizationCode(authorizationCode);
            log.info("Código de autorización procesado exitosamente");
            
            // Redirigir a una página de éxito o mostrar mensaje
            String successHtml = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Autorización Exitosa</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                        .success { color: green; }
                        .container { max-width: 500px; margin: 0 auto; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1 class="success">✅ Autorización Exitosa</h1>
                        <p>Google Drive ha sido autorizado correctamente para MIDAS CRM.</p>
                        <p>Puedes cerrar esta ventana y regresar a la aplicación.</p>
                    </div>
                </body>
                </html>
                """;
            
            response.setContentType("text/html");
            return ResponseEntity.ok(successHtml);
            
        } catch (Exception e) {
            log.error("Error procesando callback de OAuth2: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error interno procesando la autorización");
        }
    }
    
    /**
     * Endpoint para iniciar el flujo de autorización OAuth2
     * Este endpoint puede ser usado para redirigir al usuario a Google
     */
    @GetMapping("/oauth2/authorize")
    public ResponseEntity<String> initiateOAuth2Flow() {
        try {
            // Generar URL de autorización
            String authorizationUrl = googleOAuthService.getAuthorizationUrl();
            log.info("Redirigiendo a URL de autorización de Google");

            // Redirigir al usuario a Google para autorización
            String redirectHtml = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Redirigiendo a Google Drive</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                        .info { color: blue; }
                        .container { max-width: 500px; margin: 0 auto; }
                        .btn { background-color: #4285f4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
                    </style>
                    <script>
                        // Redirigir automáticamente después de 3 segundos
                        setTimeout(function() {
                            window.location.href = '%s';
                        }, 3000);
                    </script>
                </head>
                <body>
                    <div class="container">
                        <h1 class="info">🔐 Autorización Google Drive</h1>
                        <p>Serás redirigido a Google para autorizar el acceso...</p>
                        <p><a href="%s" class="btn">Autorizar Ahora</a></p>
                        <p><small>Redirección automática en 3 segundos</small></p>
                    </div>
                </body>
                </html>
                """.formatted(authorizationUrl, authorizationUrl);

            return ResponseEntity.ok(redirectHtml);

        } catch (Exception e) {
            log.error("Error generando URL de autorización: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error generando URL de autorización: " + e.getMessage());
        }
    }

    /**
     * Endpoint para verificar el estado de OAuth
     */
    @GetMapping("/oauth2/status")
    public ResponseEntity<String> checkOAuthStatus() {
        try {
            boolean hasCredentials = googleOAuthService.hasValidCredentials();

            String statusHtml = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Estado OAuth Google Drive</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                        .success { color: green; }
                        .error { color: red; }
                        .container { max-width: 500px; margin: 0 auto; }
                        .btn { background-color: #4285f4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px; }
                        .btn-danger { background-color: #dc3545; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🔐 Estado OAuth Google Drive</h1>
                        %s
                        <p><a href="/oauth2/authorize" class="btn">Autorizar</a></p>
                        %s
                    </div>
                </body>
                </html>
                """.formatted(
                    hasCredentials ?
                        "<p class=\"success\">✅ OAuth autorizado correctamente</p>" :
                        "<p class=\"error\">❌ OAuth no autorizado</p>",
                    hasCredentials ?
                        "<p><a href=\"/oauth2/revoke\" class=\"btn btn-danger\">Revocar Autorización</a></p>" :
                        ""
                );

            return ResponseEntity.ok(statusHtml);

        } catch (Exception e) {
            log.error("Error verificando estado OAuth: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error verificando estado OAuth: " + e.getMessage());
        }
    }

    /**
     * Endpoint para revocar credenciales OAuth
     */
    @GetMapping("/oauth2/revoke")
    public ResponseEntity<String> revokeOAuth() {
        try {
            googleOAuthService.revokeCredentials();

            String revokeHtml = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>OAuth Revocado</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; margin-top: 50px; }
                        .success { color: green; }
                        .container { max-width: 500px; margin: 0 auto; }
                        .btn { background-color: #4285f4; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1 class="success">✅ OAuth Revocado</h1>
                        <p>Las credenciales de Google Drive han sido revocadas exitosamente.</p>
                        <p><a href="/oauth2/authorize" class="btn">Autorizar Nuevamente</a></p>
                        <p><a href="/oauth2/status" class="btn">Ver Estado</a></p>
                    </div>
                </body>
                </html>
                """;

            return ResponseEntity.ok(revokeHtml);

        } catch (Exception e) {
            log.error("Error revocando OAuth: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error revocando OAuth: " + e.getMessage());
        }
    }

    /**
     * Endpoint para verificar el estado detallado de los tokens
     */
    @GetMapping("/oauth2/token-status")
    @ResponseBody
    public ResponseEntity<String> getTokenStatus() {
        try {
            if (!googleOAuthService.hasValidCredentials()) {
                return ResponseEntity.ok("❌ No hay credenciales OAuth válidas");
            }

            Credential credential = googleOAuthService.getStoredCredential();
            if (credential == null) {
                return ResponseEntity.ok("❌ No se pudieron cargar las credenciales");
            }

            Long expiresInSeconds = credential.getExpiresInSeconds();
            if (expiresInSeconds == null) {
                return ResponseEntity.ok("✅ Token OAuth válido (sin información de expiración)");
            }

            if (expiresInSeconds <= 0) {
                return ResponseEntity.ok("⚠️ Token OAuth expirado");
            } else if (expiresInSeconds <= 7200) {
                return ResponseEntity.ok(String.format("⚠️ Token OAuth se renovará pronto (%ds restantes)", expiresInSeconds));
            } else {
                return ResponseEntity.ok(String.format("✅ Token OAuth válido (expira en %ds)", expiresInSeconds));
            }

        } catch (Exception e) {
            log.error("Error obteniendo estado de tokens: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body("Error obteniendo estado de tokens: " + e.getMessage());
        }
    }
}
