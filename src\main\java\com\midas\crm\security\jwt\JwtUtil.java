package com.midas.crm.security.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;

/**
 * Utilidad para validar tokens JWT sin depender de HttpServletRequest
 */
@Component
@Slf4j
public class JwtUtil {

    @Value("${app.jwt.secret}")
    private String JWT_SECRET;

    /**
     * Valida un token JWT
     * @param token Token JWT a validar
     * @return true si el token es válido, false en caso contrario
     */
    public boolean validateToken(String token) {
        try {
            Claims claims = extractClaims(token);
            return claims != null && !claims.getExpiration().before(new Date());
        } catch (Exception e) {
            log.error("Error al validar token JWT: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Extrae el nombre de usuario de un token JWT
     * @param token Token JWT
     * @return Nombre de usuario
     */
    public String extractUsername(String token) {
        Claims claims = extractClaims(token);
        return claims != null ? claims.getSubject() : null;
    }

    /**
     * Extrae el ID de usuario de un token JWT
     * @param token Token JWT
     * @return ID de usuario
     */
    public Long extractUserId(String token) {
        Claims claims = extractClaims(token);
        return claims != null ? claims.get("userId", Long.class) : null;
    }

    /**
     * Extrae los claims de un token JWT
     * @param token Token JWT
     * @return Claims del token
     */
    private Claims extractClaims(String token) {
        if (token == null) return null;

        try {
            Key key = Keys.hmacShaKeyFor(JWT_SECRET.getBytes(StandardCharsets.UTF_8));
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            return e.getClaims(); // Permite usar claims aunque esté expirado
        } catch (Exception e) {
            log.error("Error al extraer claims de token JWT: {}", e.getMessage());
            return null;
        }
    }
}
