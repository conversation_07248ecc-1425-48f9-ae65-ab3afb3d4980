package com.midas.crm.controller;

import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaDTO;
import com.midas.crm.service.PreguntaEncuestaService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.preguntas-encuesta}")
@RequiredArgsConstructor
@Slf4j
public class PreguntaEncuestaController {

    private final PreguntaEncuestaService preguntaEncuestaService;

    /**
     * Crea una nueva pregunta para una encuesta
     * Implementado con programación funcional
     */
    @PostMapping
    public ResponseEntity<GenericResponse<PreguntaEncuestaDTO>> createPregunta(@Valid @RequestBody PreguntaEncuestaCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(preguntaEncuestaService::createPregunta)
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta creada exitosamente", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una pregunta por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<PreguntaEncuestaDTO>> getPregunta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(preguntaEncuestaService::getPreguntaById)
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta encontrada", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una pregunta completa (con opciones) por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}/completa")
    public ResponseEntity<GenericResponse<PreguntaEncuestaDTO>> getPreguntaCompleta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(preguntaEncuestaService::getPreguntaCompleta)
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta completa encontrada", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Actualiza una pregunta existente
     * Implementado con programación funcional
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<PreguntaEncuestaDTO>> updatePregunta(
            @PathVariable Long id,
            @Valid @RequestBody PreguntaEncuestaCreateDTO dto) {
        
        return Optional.ofNullable(dto)
            .map(preguntaDTO -> preguntaEncuestaService.updatePregunta(id, preguntaDTO))
            .map(pregunta -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta actualizada exitosamente", pregunta)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Elimina una pregunta (cambio de estado a inactivo)
     * Implementado con programación funcional
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<GenericResponse<Void>> deletePregunta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(preguntaId -> {
                preguntaEncuestaService.deletePregunta(preguntaId);
                return ResponseEntity.ok(
                    new GenericResponse<Void>(GenericResponseConstants.SUCCESS, "Pregunta eliminada exitosamente", null)
                );
            })
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene todas las preguntas de una encuesta
     * Implementado con programación funcional
     */
    @GetMapping("/encuesta/{encuestaId}")
    public ResponseEntity<GenericResponse<List<PreguntaEncuestaDTO>>> getPreguntasByEncuestaId(@PathVariable Long encuestaId) {
        return Optional.ofNullable(encuestaId)
            .map(preguntaEncuestaService::getPreguntasByEncuestaId)
            .map(preguntas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Preguntas encontradas", preguntas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene estadísticas de respuestas para una pregunta específica
     * Implementado con programación funcional
     */
    @GetMapping("/{id}/estadisticas")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getEstadisticasPregunta(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(preguntaEncuestaService::getEstadisticasPregunta)
            .map(estadisticas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Estadísticas encontradas", estadisticas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Reordena las preguntas de una encuesta
     * Implementado con programación funcional
     */
    @PutMapping("/encuesta/{encuestaId}/reordenar")
    public ResponseEntity<GenericResponse<List<PreguntaEncuestaDTO>>> reordenarPreguntas(
            @PathVariable Long encuestaId,
            @RequestBody List<Long> nuevosOrdenes) {
        
        return Optional.ofNullable(nuevosOrdenes)
            .map(ordenes -> preguntaEncuestaService.reordenarPreguntas(encuestaId, ordenes))
            .map(preguntas -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Preguntas reordenadas exitosamente", preguntas)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }
}
