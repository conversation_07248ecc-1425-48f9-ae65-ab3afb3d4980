package com.midas.crm.event;

import com.midas.crm.entity.Anuncio;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se crea un nuevo anuncio
 */
@Getter
public class AnuncioCreatedEvent extends ApplicationEvent {
    
    private final Anuncio anuncio;
    
    public AnuncioCreatedEvent(Object source, Anuncio anuncio) {
        super(source);
        this.anuncio = anuncio;
    }
}
