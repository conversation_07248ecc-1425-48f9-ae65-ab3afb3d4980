package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionCreateDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionUpdateDTO;
import com.midas.crm.entity.Modulo;
import com.midas.crm.entity.Seccion;

import java.util.List;
import java.util.stream.Collectors;

public final class SeccionMapper {

    private SeccionMapper() {}

    public static Seccion toEntity(SeccionCreateDTO dto, Modulo modulo) {
        Seccion seccion = new Seccion();
        seccion.setTitulo(dto.getTitulo());
        seccion.setDescripcion(dto.getDescripcion());
        seccion.setOrden(dto.getOrden());
        seccion.setModulo(modulo);
        seccion.setEstado("A");
        return seccion;
    }

    public static SeccionDTO toDTO(Seccion seccion) {
        if (seccion == null) return null;

        List<LeccionDTO> leccionesDTO = null;
        if (seccion.getLecciones() != null && !seccion.getLecciones().isEmpty()) {
            leccionesDTO = seccion.getLecciones().stream()
                    .map(LeccionMapper::toDTO)
                    .collect(Collectors.toList());
        }

        return new SeccionDTO(
                seccion.getId(),
                seccion.getTitulo(),
                seccion.getDescripcion(),
                seccion.getOrden(),
                seccion.getModulo() != null ? seccion.getModulo().getId() : null,
                seccion.getModulo() != null ? seccion.getModulo().getTitulo() : null,
                seccion.getEstado(),
                seccion.getFechaCreacion(),
                leccionesDTO,
                false // Por defecto, no está completado
        );
    }

    public static SeccionDTO toDTOWithoutLecciones(Seccion seccion) {
        if (seccion == null) return null;

        return new SeccionDTO(
                seccion.getId(),
                seccion.getTitulo(),
                seccion.getDescripcion(),
                seccion.getOrden(),
                seccion.getModulo() != null ? seccion.getModulo().getId() : null,
                seccion.getModulo() != null ? seccion.getModulo().getTitulo() : null,
                seccion.getEstado(),
                seccion.getFechaCreacion(),
                null,
                false // Por defecto, no está completado
        );
    }

    public static void updateEntity(Seccion seccion, SeccionUpdateDTO dto) {
        if (dto.getTitulo() != null) seccion.setTitulo(dto.getTitulo());
        if (dto.getDescripcion() != null) seccion.setDescripcion(dto.getDescripcion());
        if (dto.getOrden() != null) seccion.setOrden(dto.getOrden());
        if (dto.getEstado() != null) seccion.setEstado(dto.getEstado());
    }
}
