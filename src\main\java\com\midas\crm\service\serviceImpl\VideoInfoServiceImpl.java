package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.video.VideoInfoCreateDTO;
import com.midas.crm.entity.DTO.video.VideoInfoDTO;
import com.midas.crm.entity.Leccion;
import com.midas.crm.entity.VideoInfo;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.VideoInfoMapper;
import com.midas.crm.repository.LeccionRepository;
import com.midas.crm.repository.VideoInfoRepository;
import com.midas.crm.service.VideoInfoService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
public class VideoInfoServiceImpl implements VideoInfoService {

    private final VideoInfoRepository videoInfoRepository;
    private final LeccionRepository leccionRepository;

    @Override
    @Transactional
    public VideoInfoDTO createVideoInfo(VideoInfoCreateDTO dto) {
        // Obtener la lección
        Leccion leccion = leccionRepository.findById(dto.getLeccionId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.LECCION_NOT_FOUND));

        // Verificar si ya existe información de video para esta lección
        if (videoInfoRepository.findByLeccion(leccion).isPresent()) {
            throw new MidasExceptions(MidasErrorMessage.VIDEO_INFO_ALREADY_EXISTS);
        }

        // Crear la información del video
        VideoInfo videoInfo = VideoInfoMapper.toEntity(dto, leccion);
        
        return VideoInfoMapper.toDTO(videoInfoRepository.save(videoInfo));
    }

    @Override
    @Transactional(readOnly = true)
    public VideoInfoDTO getVideoInfoByLeccionId(Long leccionId) {
        return videoInfoRepository.findByLeccionId(leccionId)
                .map(VideoInfoMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.VIDEO_INFO_NOT_FOUND));
    }

    @Override
    @Transactional
    public void deleteVideoInfo(Long id) {
        if (!videoInfoRepository.existsById(id)) {
            throw new MidasExceptions(MidasErrorMessage.VIDEO_INFO_NOT_FOUND);
        }
        
        videoInfoRepository.deleteById(id);
    }
}
