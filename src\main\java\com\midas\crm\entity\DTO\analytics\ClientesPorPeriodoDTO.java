package com.midas.crm.entity.DTO.analytics;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * DTO para representar la cantidad de clientes por periodo (día, mes, año)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClientesPorPeriodoDTO {
    private LocalDate fecha;
    private String periodo; // "dia", "mes", "año"
    private long cantidad;
    private double tasaCrecimiento; // Porcentaje de crecimiento respecto al periodo anterior
}
