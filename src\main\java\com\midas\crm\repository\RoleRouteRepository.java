package com.midas.crm.repository;

import com.midas.crm.entity.Role;
import com.midas.crm.entity.RoleRoute;
import com.midas.crm.entity.Route;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRouteRepository extends JpaRepository<RoleRoute, Long> {

    List<RoleRoute> findByRole(Role role);

    List<RoleRoute> findByRoute(Route route);

    Optional<RoleRoute> findByRoleAndRoute(Role role, Route route);

    @Query("SELECT rr FROM RoleRoute rr WHERE rr.role = :role AND rr.canAccess = true")
    List<RoleRoute> findAccessibleRoutesByRole(@Param("role") Role role);

    @Query("SELECT rr FROM RoleRoute rr WHERE rr.route.id = :routeId AND rr.canAccess = true")
    List<RoleRoute> findAccessibleRolesByRouteId(@Param("routeId") Long routeId);

    @Query("SELECT DISTINCT rr.role FROM RoleRoute rr WHERE rr.route.path = :path AND rr.canAccess = true")
    List<Role> findRolesByRoutePath(@Param("path") String path);

    void deleteByRoleAndRoute(Role role, Route route);
}
