package com.midas.crm.service.serviceImpl;

import com.google.api.client.http.InputStreamContent;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.File;
import com.google.api.services.drive.model.FileList;
import com.google.api.services.drive.model.Permission;
import com.midas.crm.service.GoogleDriveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * Implementación del servicio de Google Drive
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class GoogleDriveServiceImpl implements GoogleDriveService {

    private final Drive driveService;

    @Value("${google.drive.shared.email:}")
    private String sharedEmail;

    @Value("${google.drive.auto-share.enabled:false}")
    private boolean autoShareEnabled;

    @Override
    public String uploadFile(MultipartFile file, String fileName, String folderId) throws IOException {
 //       log.info("Subiendo archivo: {} a la carpeta: {}", fileName, folderId);

        File fileMetadata = new File();
        fileMetadata.setName(fileName);

        if (folderId != null && !folderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(folderId));
        }

        InputStreamContent mediaContent = new InputStreamContent(
                file.getContentType(),
                file.getInputStream()
        );
        mediaContent.setLength(file.getSize());

        File uploadedFile = driveService.files().create(fileMetadata, mediaContent)
                .setFields("id,name,size,mimeType,createdTime")
                .execute();

 //       log.info("Archivo subido exitosamente. ID: {}", uploadedFile.getId());
        return uploadedFile.getId();
    }

    @Override
    public String uploadFile(MultipartFile file, String fileName) throws IOException {
        return uploadFile(file, fileName, null);
    }

    @Override
    public byte[] downloadFile(String fileId) throws IOException {
    //    log.info("Descargando archivo con ID: {}", fileId);

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        driveService.files().get(fileId).executeMediaAndDownloadTo(outputStream);

        byte[] content = outputStream.toByteArray();
     //   log.info("Archivo descargado exitosamente. Tamaño: {} bytes", content.length);

        return content;
    }

    @Override
    public void deleteFile(String fileId) throws IOException {
    //    log.info("Eliminando archivo con ID: {}", fileId);
        driveService.files().delete(fileId).execute();
      //  log.info("Archivo eliminado exitosamente");
    }

    @Override
    public List<Map<String, Object>> listFiles(String folderId, int pageSize) throws IOException {
      //  log.info("Listando archivos en carpeta: {}, pageSize: {}", folderId, pageSize);

        String query = "trashed=false";
        if (folderId != null && !folderId.isEmpty()) {
            query += " and '" + folderId + "' in parents";
        }

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(pageSize)
                .setFields("nextPageToken, files(id,name,size,mimeType,createdTime,modifiedTime,parents)")
                .execute();

        List<Map<String, Object>> files = new ArrayList<>();
        for (File file : result.getFiles()) {
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("id", file.getId());
            fileInfo.put("name", file.getName());
            fileInfo.put("size", file.getSize());
            fileInfo.put("mimeType", file.getMimeType());
            fileInfo.put("createdTime", file.getCreatedTime());
            fileInfo.put("modifiedTime", file.getModifiedTime());
            fileInfo.put("parents", file.getParents());
            files.add(fileInfo);
        }

    //    log.info("Se encontraron {} archivos", files.size());
        return files;
    }

    @Override
    public List<Map<String, Object>> listFiles(int pageSize) throws IOException {
        return listFiles(null, pageSize);
    }

    @Override
    public String createFolder(String folderName, String parentFolderId) throws IOException {
     //   log.info("Creando carpeta: {} en carpeta padre: {}", folderName, parentFolderId);

        File fileMetadata = new File();
        fileMetadata.setName(folderName);
        fileMetadata.setMimeType("application/vnd.google-apps.folder");

        if (parentFolderId != null && !parentFolderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(parentFolderId));
        }

        File folder = driveService.files().create(fileMetadata)
                .setFields("id,name")
                .execute();

    //    log.info("Carpeta creada exitosamente. ID: {}", folder.getId());

        // Intentar compartir automáticamente solo si está habilitado y configurado
        if (autoShareEnabled && sharedEmail != null && !sharedEmail.isEmpty()) {
            try {
                shareFileWithRetry(folder.getId(), sharedEmail, "writer");
             //   log.info("Carpeta compartida automáticamente con {}", sharedEmail);
            } catch (Exception e) {
               // log.warn("No se pudo compartir automáticamente la carpeta con {}: {}", sharedEmail, e.getMessage());
                // No lanzar excepción, solo registrar el warning
            }
        }

        return folder.getId();
    }

    @Override
    public String createFolder(String folderName) throws IOException {
        return createFolder(folderName, null);
    }

    /**
     * Crea una carpeta en una ubicación específica accesible para el usuario objetivo
     */
    public String createFolderInSharedLocation(String folderName, String sharedParentFolderId) throws IOException {
   //     log.info("Creando carpeta: {} en ubicación compartida: {}", folderName, sharedParentFolderId);

        File fileMetadata = new File();
        fileMetadata.setName(folderName);
        fileMetadata.setMimeType("application/vnd.google-apps.folder");

        if (sharedParentFolderId != null && !sharedParentFolderId.isEmpty()) {
            fileMetadata.setParents(Collections.singletonList(sharedParentFolderId));
        }

        File folder = driveService.files().create(fileMetadata)
                .setFields("id,name")
                .execute();

     //   log.info("Carpeta creada exitosamente en ubicación compartida. ID: {}", folder.getId());
        return folder.getId();
    }

    @Override
    public Map<String, Object> getFileInfo(String fileId) throws IOException {
    //    log.info("Obteniendo información del archivo: {}", fileId);

        File file = driveService.files().get(fileId)
                .setFields("id,name,size,mimeType,createdTime,modifiedTime,parents,webViewLink,webContentLink")
                .execute();

        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("id", file.getId());
        fileInfo.put("name", file.getName());
        fileInfo.put("size", file.getSize());
        fileInfo.put("mimeType", file.getMimeType());
        fileInfo.put("createdTime", file.getCreatedTime());
        fileInfo.put("modifiedTime", file.getModifiedTime());
        fileInfo.put("parents", file.getParents());
        fileInfo.put("webViewLink", file.getWebViewLink());
        fileInfo.put("webContentLink", file.getWebContentLink());

        return fileInfo;
    }

    @Override
    public String shareFile(String fileId, String email, String role) throws IOException {
        return shareFileWithRetry(fileId, email, role);
    }

    /**
     * Comparte un archivo con reintentos y mejor manejo de errores
     */
    private String shareFileWithRetry(String fileId, String email, String role) throws IOException {
     //   log.info("Compartiendo archivo {} con {} con rol: {}", fileId, email, role);

        try {
            Permission permission = new Permission();
            permission.setType("user");
            permission.setRole(role);
            permission.setEmailAddress(email);

            Permission createdPermission = driveService.permissions().create(fileId, permission)
                    .setFields("id")
                    .setSendNotificationEmail(false) // Evitar envío de notificaciones
                    .execute();

        //    log.info("Archivo compartido exitosamente. Permission ID: {}", createdPermission.getId());
            return createdPermission.getId();

        } catch (IOException e) {
            // Si falla el compartir directo, intentar hacer público primero
            if (e.getMessage().contains("insufficient permissions") ||
                    e.getMessage().contains("permission denied")) {

            //   log.warn("Permisos insuficientes para compartir directamente. Intentando hacer público...");
                return makeFilePublicAndGetLink(fileId);
            }
            throw e;
        }
    }

    /**
     * Hace un archivo público y retorna el enlace
     */
    private String makeFilePublicAndGetLink(String fileId) throws IOException {
        Permission permission = new Permission();
        permission.setType("anyone");
        permission.setRole("reader");

        Permission createdPermission = driveService.permissions().create(fileId, permission)
                .setFields("id")
                .execute();

        //log.info("Archivo hecho público. Permission ID: {}", createdPermission.getId());
        return createdPermission.getId();
    }

    @Override
    public String getPublicLink(String fileId) throws IOException {
       // log.info("Obteniendo enlace público para archivo: {}", fileId);

        // Hacer el archivo público
        Permission permission = new Permission();
        permission.setType("anyone");
        permission.setRole("reader");

        driveService.permissions().create(fileId, permission).execute();

        // Obtener el enlace
        File file = driveService.files().get(fileId)
                .setFields("webViewLink,webContentLink")
                .execute();

        String publicLink = file.getWebViewLink();
     //   log.info("Enlace público generado: {}", publicLink);

        return publicLink;
    }

    @Override
    public String findFolderByName(String folderName) throws IOException {
    //    log.info("Buscando carpeta con nombre: {}", folderName);

        String query = "mimeType='application/vnd.google-apps.folder' and name='" + folderName + "' and trashed=false";

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(1)
                .setFields("files(id,name)")
                .execute();

        List<File> folders = result.getFiles();
        if (folders != null && !folders.isEmpty()) {
            String folderId = folders.get(0).getId();
       //     log.info("Carpeta '{}' encontrada con ID: {}", folderName, folderId);
            return folderId;
        } else {
         //   log.warn("Carpeta '{}' no encontrada", folderName);
            return null;
        }
    }

    /**
     * Busca o crea una carpeta compartida
     */
    public String findOrCreateSharedFolder(String folderName, String userEmail) throws IOException {
      //  log.info("Buscando o creando carpeta compartida: {} para usuario: {}", folderName, userEmail);

        // Primero buscar si ya existe
        String existingFolderId = findFolderByName(folderName);
        if (existingFolderId != null) {
        //    log.info("Carpeta existente encontrada: {}", existingFolderId);
            return existingFolderId;
        }

        // Si no existe, crear nueva carpeta
        String folderId = createFolder(folderName);

        // Intentar compartir
        try {
            shareFile(folderId, userEmail, "writer");
         //   log.info("Carpeta compartida exitosamente con: {}", userEmail);
        } catch (Exception e) {
           // log.warn("No se pudo compartir la carpeta, pero fue creada exitosamente: {}", e.getMessage());
        }

        return folderId;
    }

    @Override
    public List<Map<String, Object>> listFolders(int pageSize) throws IOException {
      //  log.info("Listando carpetas, pageSize: {}", pageSize);

        String query = "mimeType='application/vnd.google-apps.folder' and trashed=false";

        FileList result = driveService.files().list()
                .setQ(query)
                .setPageSize(pageSize)
                .setFields("files(id,name,createdTime,modifiedTime)")
                .execute();

        List<File> folders = result.getFiles();
        List<Map<String, Object>> folderList = new ArrayList<>();

        if (folders != null) {
            for (File folder : folders) {
                Map<String, Object> folderInfo = new HashMap<>();
                folderInfo.put("id", folder.getId());
                folderInfo.put("name", folder.getName());
                folderInfo.put("createdTime", folder.getCreatedTime());
                folderInfo.put("modifiedTime", folder.getModifiedTime());
                folderInfo.put("type", "folder");
                folderList.add(folderInfo);
            }
        }

      // log.info("Se encontraron {} carpetas", folderList.size());
        return folderList;
    }
}