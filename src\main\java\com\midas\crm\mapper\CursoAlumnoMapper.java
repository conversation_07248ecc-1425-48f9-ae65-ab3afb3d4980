package com.midas.crm.mapper;

import com.midas.crm.entity.CursoUsuario;
import com.midas.crm.entity.DTO.curso.CursoAlumnoDTO;

import java.util.Map;

/**
 * Mapper para convertir datos de CursoUsuario a CursoAlumnoDTO optimizado
 */
public class CursoAlumnoMapper {

    /**
     * Convierte un CursoUsuario a CursoAlumnoDTO
     * 
     * @param cursoUsuario La entidad CursoUsuario
     * @param progreso     Mapa con información de progreso (opcional)
     * @return CursoAlumnoDTO optimizado
     */
    public static CursoAlumnoDTO toDTO(CursoUsuario cursoUsuario, Map<String, Object> progreso) {
        if (cursoUsuario == null) {
            return null;
        }

        CursoAlumnoDTO dto = new CursoAlumnoDTO();

        // Información de la asignación
        dto.setId(cursoUsuario.getId());
        dto.setFechaAsignacion(cursoUsuario.getFechaAsignacion());
        dto.setEstado(cursoUsuario.getEstado());
        dto.setCompletado(cursoUsuario.isCompletado());
        dto.setFechaCompletado(cursoUsuario.getFechaCompletado());
        dto.setPorcentajeCompletado(cursoUsuario.getPorcentajeCompletado());
        dto.setUltimaVisualizacion(cursoUsuario.getUltimaVisualizacion());

        // Información del usuario (solo campos necesarios)
        if (cursoUsuario.getUsuario() != null) {
            CursoAlumnoDTO.UsuarioDTO usuarioDTO = new CursoAlumnoDTO.UsuarioDTO();
            usuarioDTO.setId(cursoUsuario.getUsuario().getId());
            usuarioDTO.setUsername(cursoUsuario.getUsuario().getUsername());
            usuarioDTO.setNombre(cursoUsuario.getUsuario().getNombre());
            usuarioDTO.setApellido(cursoUsuario.getUsuario().getApellido());
            usuarioDTO.setDni(cursoUsuario.getUsuario().getDni());
            usuarioDTO.setEmail(cursoUsuario.getUsuario().getEmail());
            usuarioDTO.setRole(
                    cursoUsuario.getUsuario().getRole() != null ? cursoUsuario.getUsuario().getRole().toString()
                            : null);
            dto.setUsuario(usuarioDTO);
        }

        // Información de progreso
        if (progreso != null) {
            CursoAlumnoDTO.ProgresoDTO progresoDTO = new CursoAlumnoDTO.ProgresoDTO();
            progresoDTO.setTotalLecciones(getIntValue(progreso, "totalLecciones", 0));
            progresoDTO.setLeccionesCompletadas(getIntValue(progreso, "leccionesCompletadas", 0));
            progresoDTO.setPorcentajeProgreso(getIntValue(progreso, "porcentajeProgreso", 0));
            dto.setProgreso(progresoDTO);
        }

        return dto;
    }

    /**
     * Convierte un CursoUsuario a CursoAlumnoDTO sin información de progreso
     * 
     * @param cursoUsuario La entidad CursoUsuario
     * @return CursoAlumnoDTO optimizado
     */
    public static CursoAlumnoDTO toDTO(CursoUsuario cursoUsuario) {
        return toDTO(cursoUsuario, null);
    }

    /**
     * Método auxiliar para extraer valores enteros del mapa de progreso
     */
    private static int getIntValue(Map<String, Object> map, String key, int defaultValue) {
        Object value = map.get(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return defaultValue;
    }
}
