package com.midas.crm.controller;

import com.midas.crm.entity.DTO.queue.TranscriptionQueueMessage;
import com.midas.crm.service.TranscriptionQueueService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Controlador para gestionar las colas de transcripción automática
 */
@RestController
@RequestMapping("${api.route.transcription-queue:/api/transcription-queue}")
@RequiredArgsConstructor
@Slf4j
public class TranscriptionQueueController {

    private final TranscriptionQueueService transcriptionQueueService;

    /**
     * Procesa leads sin transcripción y los envía a la cola de transcripción
     * Procesa máximo 3 leads simultáneamente
     */
    @PostMapping("/process-pending-leads")
    public ResponseEntity<GenericResponse<Map<String, Object>>> processPendingLeads(
            @RequestParam(defaultValue = "10") int batchSize,
            @RequestParam(required = false) String numeroAgente) {

        try {
            log.info("Iniciando procesamiento de leads pendientes. BatchSize: {}, NumeroAgente: {}",
                    batchSize, numeroAgente);

            // Procesar de forma asíncrona
            CompletableFuture<Map<String, Object>> result = CompletableFuture
                    .supplyAsync(() -> transcriptionQueueService.processPendingLeads(batchSize, numeroAgente));

            Map<String, Object> response = result.join();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de leads iniciado correctamente",
                    response
            ));

        } catch (Exception e) {
            log.error("Error al procesar leads pendientes", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al procesar leads: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Obtiene estadísticas de las colas de transcripción
     */
    @GetMapping("/queue-stats")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getQueueStats() {
        try {
            Map<String, Object> stats = transcriptionQueueService.getQueueStatistics();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Estadísticas obtenidas correctamente",
                    stats
            ));

        } catch (Exception e) {
            log.error("Error al obtener estadísticas de cola", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al obtener estadísticas: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Obtiene la lista de leads pendientes de transcripción
     */
    @GetMapping("/pending-leads")
    public ResponseEntity<GenericResponse<List<TranscriptionQueueMessage>>> getPendingLeads(
            @RequestParam(defaultValue = "50") int limit,
            @RequestParam(required = false) String numeroAgente) {

        try {
            List<TranscriptionQueueMessage> pendingLeads =
                    transcriptionQueueService.getPendingLeads(limit, numeroAgente);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    String.format("Se encontraron %d leads pendientes", pendingLeads.size()),
                    pendingLeads
            ));

        } catch (Exception e) {
            log.error("Error al obtener leads pendientes", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al obtener leads pendientes: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Reintenta el procesamiento de mensajes fallidos en la DLQ
     */
    @PostMapping("/retry-failed")
    public ResponseEntity<GenericResponse<Map<String, Object>>> retryFailedMessages(
            @RequestParam(defaultValue = "10") int maxRetries) {

        try {
            log.info("Reintentando mensajes fallidos. MaxRetries: {}", maxRetries);

            Map<String, Object> result = transcriptionQueueService.retryFailedMessages(maxRetries);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Reintento de mensajes fallidos iniciado",
                    result
            ));

        } catch (Exception e) {
            log.error("Error al reintentar mensajes fallidos", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al reintentar mensajes: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Envía un lead específico a la cola de transcripción
     */
    @PostMapping("/send-to-queue/{leadId}")
    public ResponseEntity<GenericResponse<String>> sendLeadToQueue(@PathVariable Long leadId) {
        try {
            log.info("Enviando lead {} a la cola de transcripción", leadId);

            boolean sent = transcriptionQueueService.sendLeadToQueue(leadId);

            if (sent) {
                return ResponseEntity.ok(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Lead enviado a la cola correctamente",
                        "Lead ID: " + leadId
                ));
            } else {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "No se pudo enviar el lead a la cola",
                                null
                        ));
            }

        } catch (Exception e) {
            log.error("Error al enviar lead {} a la cola", leadId, e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al enviar lead a la cola: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Pausa el procesamiento de las colas
     */
    @PostMapping("/pause")
    public ResponseEntity<GenericResponse<String>> pauseQueueProcessing() {
        try {
            transcriptionQueueService.pauseProcessing();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de colas pausado",
                    "Las colas han sido pausadas correctamente"
            ));

        } catch (Exception e) {
            log.error("Error al pausar procesamiento de colas", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al pausar procesamiento: " + e.getMessage(),
                            null
                    ));
        }
    }

    /**
     * Reanuda el procesamiento de las colas
     */
    @PostMapping("/resume")
    public ResponseEntity<GenericResponse<String>> resumeQueueProcessing() {
        try {
            transcriptionQueueService.resumeProcessing();

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Procesamiento de colas reanudado",
                    "Las colas han sido reanudadas correctamente"
            ));

        } catch (Exception e) {
            log.error("Error al reanudar procesamiento de colas", e);
            return ResponseEntity.internalServerError()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al reanudar procesamiento: " + e.getMessage(),
                            null
                    ));
        }
    }
}
