package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.asesor.AsesorDTO;
import com.midas.crm.entity.DTO.cliente.ClienteResidencialDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.AsesorMapper;
import com.midas.crm.repository.UserRepository;
import com.midas.crm.service.AsesorService;
import com.midas.crm.service.ClienteResidencialExcelService;
import com.midas.crm.service.ClienteResidencialService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AsesorServiceImpl implements AsesorService {

    private final UserRepository userRepository;
    private final ClienteResidencialService clienteResidencialService;
    private final ClienteResidencialExcelService clienteResidencialExcelService;

    @Override
    @Transactional(readOnly = true)
    public List<AsesorDTO> getAllAsesores() {
        List<User> asesores = userRepository.findByRole(Role.ASESOR);

        // Para cada asesor, asegurarse de que la sede se cargue correctamente
        return asesores.stream()
                .map(asesor -> {
                    // Inicializar la sede si es necesario (esto fuerza la carga de la relación)
                    if (asesor.getSede() != null) {
                        asesor.getSede().getNombre(); // Forzar la inicialización
                    }
                    return AsesorMapper.toDTO(asesor);
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AsesorDTO> getAsesorById(Long id) {
        return userRepository.findByIdAndRole(id, Role.ASESOR)
                .map(asesor -> {
                    // Inicializar la sede si es necesario (esto fuerza la carga de la relación)
                    if (asesor.getSede() != null) {
                        asesor.getSede().getNombre(); // Forzar la inicialización
                    }
                    return AsesorMapper.toDTO(asesor);
                });
    }

    @Override
    @Transactional(readOnly = true)
    public List<AsesorDTO> getAsesoresByCoordinadorId(Long coordinadorId) {
        List<User> asesores = userRepository.findByCoordinadorIdAndRole(coordinadorId, Role.ASESOR);

        // Para cada asesor, asegurarse de que la sede se cargue correctamente
        return asesores.stream()
                .map(asesor -> {
                    // Inicializar la sede si es necesario (esto fuerza la carga de la relación)
                    if (asesor.getSede() != null) {
                        asesor.getSede().getNombre(); // Forzar la inicialización
                    }
                    return AsesorMapper.toDTO(asesor);
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public AsesorDTO convertToDTO(User user) {
        // Inicializar la sede si es necesario (esto fuerza la carga de la relación)
        if (user.getSede() != null) {
            user.getSede().getNombre(); // Forzar la inicialización
        }
        return AsesorMapper.toDTO(user);
    }

    @Override
    public ResponseEntity<List<ClienteResidencialDTO>> getClientesByAsesor(Long id) {
        // Verificar primero si el asesor existe
        if (getAsesorById(id).isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        List<ClienteResidencialDTO> clientes = clienteResidencialService.getClientesByAsesorId(id);
        return ResponseEntity.ok(clientes);
    }

    @Override
    public ResponseEntity<Map<String, Object>> getEstadisticasByAsesor(Long id) {
        // Verificar primero si el asesor existe
        if (getAsesorById(id).isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        Map<String, Object> estadisticas = new HashMap<>();
        Long totalClientes = clienteResidencialService.countClientesByAsesorId(id);
        List<ClienteResidencialDTO> ventasRealizadas = clienteResidencialService.getVentasRealizadasByAsesorId(id);

        estadisticas.put("totalClientes", totalClientes);
        estadisticas.put("ventasRealizadas", ventasRealizadas.size());
        estadisticas.put("porcentajeExito",
                totalClientes > 0 ? (double) ventasRealizadas.size() / totalClientes * 100 : 0);

        return ResponseEntity.ok(estadisticas);
    }

    @Override
    public ResponseEntity<List<ClienteResidencialDTO>> getVentasByAsesor(Long id) {
        // Verificar primero si el asesor existe
        if (getAsesorById(id).isEmpty()) {
            return ResponseEntity.notFound().build();
        }

        List<ClienteResidencialDTO> ventas = clienteResidencialService.getVentasRealizadasByAsesorId(id);
        return ResponseEntity.ok(ventas);
    }

    @Override
    public ResponseEntity<byte[]> exportarClientesHoyDeAsesores(Long coordinadorId) {
        byte[] data = clienteResidencialExcelService.exportarClientesDeAsesoresAsignadosHoy(coordinadorId);

        if (data.length == 0)
            return ResponseEntity.noContent().build();

        String fecha = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentDisposition(ContentDisposition.builder("attachment")
                .filename("clientes_asesores_" + fecha + ".xlsx")
                .build());
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        return new ResponseEntity<>(data, headers, HttpStatus.OK);
    }
}