package com.midas.crm.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

/**
 * Controlador para endpoints de verificación de salud del sistema
 * Estos endpoints son públicos y no requieren autenticación
 */
@RestController
@RequestMapping("/api/health")
@Slf4j
public class HealthCheckController {

    /**
     * Endpoint simple para verificar si el servidor está disponible
     * No requiere autenticación y es útil para verificaciones de disponibilidad
     * @return 200 OK con el texto "OK" si el servidor está disponible
     */
    @GetMapping("/ping")
    public ResponseEntity<String> ping() {
        return ResponseEntity.ok("OK");
    }

    /**
     * Endpoint para verificar la disponibilidad del servidor para WebSocket
     * Este endpoint es específicamente para verificaciones de CORS desde el frontend
     * @return 200 OK con el texto "OK" si el servidor está disponible
     */
    @GetMapping("/ws-check")
    public ResponseEntity<String> wsCheck() {
        log.info("Verificación de disponibilidad WebSocket recibida");
        return ResponseEntity.ok("OK");
    }
}
