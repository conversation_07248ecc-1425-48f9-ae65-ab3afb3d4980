package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidad que representa un intento de un usuario en un cuestionario
 */
@Entity
@Table(name = "respuestas_usuario")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RespuestaUsuario {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User usuario;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cuestionario_id", nullable = false)
    private Cuestionario cuestionario;

    @Column(name = "fecha_inicio", nullable = false)
    private LocalDateTime fechaInicio;

    @Column(name = "fecha_fin")
    private LocalDateTime fechaFin;

    @Column(name = "puntaje_obtenido")
    private Integer puntajeObtenido; // Puntaje obtenido en el cuestionario

    @Column(name = "porcentaje_aprobacion")
    private Integer porcentajeAprobacion; // Porcentaje de aprobación (0-100)

    @Column(nullable = false)
    private Boolean completado = false; // Indica si el cuestionario fue completado

    @Column(nullable = false)
    private Boolean aprobado = false; // Indica si el cuestionario fue aprobado

    @Column(name = "numero_intento", nullable = false)
    private Integer numeroIntento = 1; // Número de intento del usuario

    @OneToMany(mappedBy = "respuestaUsuario", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<DetalleRespuestaUsuario> detallesRespuestas = new ArrayList<>();

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;
}
