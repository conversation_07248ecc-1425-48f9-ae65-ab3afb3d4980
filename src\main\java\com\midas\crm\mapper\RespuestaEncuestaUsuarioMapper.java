package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.RespuestaEncuestaUsuarioDTO;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.RespuestaEncuestaUsuario;
import com.midas.crm.entity.User;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Mapper para convertir entre entidades RespuestaEncuestaUsuario y sus DTOs
 */
public final class RespuestaEncuestaUsuarioMapper {

    private RespuestaEncuestaUsuarioMapper() {}

    /**
     * Convierte un DTO de creación a una entidad RespuestaEncuestaUsuario
     */
    public static RespuestaEncuestaUsuario toEntity(RespuestaEncuestaUsuarioCreateDTO dto, User usuario, Encuesta encuesta) {
        RespuestaEncuestaUsuario respuesta = new RespuestaEncuestaUsuario();
        respuesta.setUsuario(usuario); // Puede ser null si la encuesta es anónima
        respuesta.setEncuesta(encuesta);
        respuesta.setFechaInicio(LocalDateTime.now());
        respuesta.setCompletada(false);
        return respuesta;
    }

    /**
     * Convierte una entidad RespuestaEncuestaUsuario a un DTO
     */
    public static RespuestaEncuestaUsuarioDTO toDTO(RespuestaEncuestaUsuario respuesta) {
        if (respuesta == null) return null;

        RespuestaEncuestaUsuarioDTO dto = new RespuestaEncuestaUsuarioDTO();
        dto.setId(respuesta.getId());
        dto.setFechaInicio(respuesta.getFechaInicio());
        dto.setFechaFin(respuesta.getFechaFin());
        dto.setCompletada(respuesta.getCompletada());
        dto.setFechaCreacion(respuesta.getFechaCreacion());
        dto.setFechaActualizacion(respuesta.getFechaActualizacion());

        // Información del usuario (si no es anónima)
        if (respuesta.getUsuario() != null) {
            dto.setUsuario(UserMapper.toDTO(respuesta.getUsuario()));
            dto.setUsuarioId(respuesta.getUsuario().getId());
            dto.setUsuarioNombre(respuesta.getUsuario().getNombre() + " " + respuesta.getUsuario().getApellido());
        }

        // Información de la encuesta
        if (respuesta.getEncuesta() != null) {
            dto.setEncuestaId(respuesta.getEncuesta().getId());
            dto.setEncuestaTitulo(respuesta.getEncuesta().getTitulo());
        }

        return dto;
    }

    /**
     * Convierte una entidad RespuestaEncuestaUsuario a un DTO incluyendo sus detalles
     */
    public static RespuestaEncuestaUsuarioDTO toDTOWithDetalles(RespuestaEncuestaUsuario respuesta, List<DetalleRespuestaEncuestaUsuarioDTO> detalles) {
        RespuestaEncuestaUsuarioDTO dto = toDTO(respuesta);
        if (dto != null) {
            dto.setDetallesRespuestas(detalles);
        }
        return dto;
    }
}
