package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.Certificado;
import com.midas.crm.entity.Curso;
import com.midas.crm.entity.CursoUsuario;
import com.midas.crm.entity.User;
import com.midas.crm.entity.DTO.certificado.CertificadoCreateDTO;
import com.midas.crm.entity.DTO.certificado.CertificadoDTO;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.CertificadoMapper;
import com.midas.crm.repository.*;
import com.midas.crm.service.CertificadoService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CertificadoServiceImpl implements CertificadoService {

    @Autowired
    private CertificadoRepository certificadoRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CursoRepository cursoRepository;

    @Autowired
    private CursoUsuarioRepository cursoUsuarioRepository;

    @Autowired
    private ProgresoUsuarioRepository progresoUsuarioRepository;

    @Autowired
    private LeccionRepository leccionRepository;

    @Override
    @Transactional
    public CertificadoDTO crearCertificado(CertificadoCreateDTO certificadoCreateDTO, Long adminId) {
        log.info("Creando certificado para usuario {} y curso {}",
                certificadoCreateDTO.getUsuarioId(), certificadoCreateDTO.getCursoId());

        // Verificar que el usuario existe
        User usuario = userRepository.findById(certificadoCreateDTO.getUsuarioId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Verificar que el curso existe
        Curso curso = cursoRepository.findById(certificadoCreateDTO.getCursoId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CURSO_NOT_FOUND));

        // Verificar que el administrador existe
        User admin = userRepository.findById(adminId)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.USUARIO_NOT_FOUND));

        // Verificar que no existe ya un certificado para este usuario y curso
        if (certificadoRepository.existsByUsuarioIdAndCursoId(
                certificadoCreateDTO.getUsuarioId(), certificadoCreateDTO.getCursoId())) {
            throw new MidasExceptions(MidasErrorMessage.CERTIFICADO_YA_EXISTE);
        }

        // Verificar que el usuario puede recibir el certificado (completó el curso)
        if (!puedeRecibirCertificado(certificadoCreateDTO.getUsuarioId(), certificadoCreateDTO.getCursoId())) {
            throw new MidasExceptions(MidasErrorMessage.CURSO_NO_COMPLETADO);
        }

        // Crear el certificado
        Certificado certificado = CertificadoMapper.toEntity(certificadoCreateDTO, usuario, curso, admin);

        // Si no se especifica fecha de emisión, usar la actual
        if (certificado.getFechaEmision() == null) {
            certificado.setFechaEmision(LocalDateTime.now());
        }

        certificado = certificadoRepository.save(certificado);

        log.info("Certificado creado exitosamente con ID: {}", certificado.getId());
        return CertificadoMapper.toDTO(certificado);
    }

    @Override
    @Transactional(readOnly = true)
    public CertificadoDTO obtenerCertificadoPorId(Long id) {
        Certificado certificado = certificadoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CERTIFICADO_NOT_FOUND));
        return CertificadoMapper.toDTO(certificado);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CertificadoDTO> obtenerCertificadosPorUsuario(Long usuarioId) {
        return certificadoRepository.findByUsuarioId(usuarioId).stream()
                .map(CertificadoMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<CertificadoDTO> obtenerCertificadosPorCurso(Long cursoId) {
        return certificadoRepository.findByCursoId(cursoId).stream()
                .map(CertificadoMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public boolean puedeRecibirCertificado(Long usuarioId, Long cursoId) {
        // Verificar que el usuario está asignado al curso
        if (!cursoUsuarioRepository.existsByCursoIdAndUsuarioId(cursoId, usuarioId)) {
            return false;
        }

        // Obtener el total de lecciones del curso
        Long totalLecciones = progresoUsuarioRepository.countTotalLeccionesByCurso(cursoId);

        // Obtener las lecciones completadas por el usuario en este curso
        Long leccionesCompletadas = progresoUsuarioRepository
                .countCompletedLeccionesByCursoAndUsuario(cursoId, usuarioId);

        // El usuario puede recibir certificado si completó todas las lecciones
        return totalLecciones > 0 && leccionesCompletadas.equals(totalLecciones);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> obtenerProgresoDetallado(Long usuarioId, Long cursoId) {
        Map<String, Object> progreso = new HashMap<>();

        // Obtener información básica
        Long totalLecciones = progresoUsuarioRepository.countTotalLeccionesByCurso(cursoId);
        Long leccionesCompletadas = progresoUsuarioRepository
                .countCompletedLeccionesByCursoAndUsuario(cursoId, usuarioId);

        double porcentajeCompletado = totalLecciones > 0
                ? (leccionesCompletadas.doubleValue() / totalLecciones.doubleValue()) * 100
                : 0;

        progreso.put("totalLecciones", totalLecciones);
        progreso.put("leccionesCompletadas", leccionesCompletadas);
        progreso.put("porcentajeCompletado", Math.round(porcentajeCompletado * 100.0) / 100.0);
        progreso.put("puedeRecibirCertificado", leccionesCompletadas.equals(totalLecciones));

        return progreso;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existeCertificado(Long usuarioId, Long cursoId) {
        return certificadoRepository.existsByUsuarioIdAndCursoId(usuarioId, cursoId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CertificadoDTO> obtenerCertificadosPaginados(String search, String estado, Pageable pageable) {
        return certificadoRepository.findBySearchTermAndEstado(search, estado, pageable)
                .map(CertificadoMapper::toDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public CertificadoDTO obtenerCertificadoPorCodigo(String codigo) {
        Certificado certificado = certificadoRepository.findByCodigoCertificado(codigo)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CERTIFICADO_NOT_FOUND,
                        "Certificado no encontrado con el código: " + codigo));
        return CertificadoMapper.toDTO(certificado);
    }

    @Override
    @Transactional
    public void eliminarCertificado(Long id) {
        Certificado certificado = certificadoRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.CERTIFICADO_NOT_FOUND));
        certificado.setEstado("I");
        certificadoRepository.save(certificado);
        log.info("Certificado con ID {} marcado como inactivo", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Map<String, Object>> obtenerUsuariosElegibles(Long cursoId) {
        // Obtener todos los usuarios asignados al curso
        List<CursoUsuario> cursosUsuarios = cursoUsuarioRepository.findByCursoIdWithDetails(cursoId);

        return cursosUsuarios.stream()
                .map(cursoUsuario -> {
                    Map<String, Object> usuarioInfo = new HashMap<>();
                    User usuario = cursoUsuario.getUsuario();

                    usuarioInfo.put("usuarioId", usuario.getId());
                    usuarioInfo.put("nombre", usuario.getNombre());
                    usuarioInfo.put("apellido", usuario.getApellido());
                    usuarioInfo.put("dni", usuario.getDni());
                    usuarioInfo.put("email", usuario.getEmail());

                    // Obtener progreso
                    Map<String, Object> progreso = obtenerProgresoDetallado(usuario.getId(), cursoId);
                    usuarioInfo.putAll(progreso);

                    // Verificar si ya tiene certificado
                    usuarioInfo.put("tieneCertificado", existeCertificado(usuario.getId(), cursoId));

                    return usuarioInfo;
                })
                .collect(Collectors.toList());
    }
}
