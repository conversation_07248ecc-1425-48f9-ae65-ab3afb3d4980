package com.midas.crm.config;

import com.midas.crm.security.CustomUserDetailsService;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtUtil;
import com.midas.crm.service.serviceImpl.UserConnectionServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Interceptor para autenticar las conexiones WebSocket
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketAuthInterceptor implements ChannelInterceptor {

    private final JwtUtil jwtUtil;
    private final CustomUserDetailsService userDetailsService;

    @Autowired
    private UserConnectionServiceImpl userConnectionService;

    // Mapa para almacenar las autenticaciones por sessionId
    private final Map<String, Authentication> sessionAuthentications = new ConcurrentHashMap<>();

    // Mapa para evitar procesar mensajes duplicados
    private final Map<String, Long> processedMessages = new ConcurrentHashMap<>();

    // Mapa para evitar procesar mensajes duplicados por destino
    private final Map<String, Long> processedDestinations = new ConcurrentHashMap<>();

    // Mapa para evitar procesar desconexiones duplicadas
    private final Map<Long, Long> processedDisconnections = new ConcurrentHashMap<>();

    // Tiempo de deduplicación para mensajes (5 segundos)
    private static final long MESSAGE_DEDUPLICATION_WINDOW = 5000;

    // Tiempo de deduplicación para destinos específicos (10 segundos)
    private static final long DESTINATION_DEDUPLICATION_WINDOW = 10000;

    // Tiempo de deduplicación para desconexiones (5 segundos)
    private static final long DISCONNECT_DEDUPLICATION_WINDOW = 5000;

    // Destinos que requieren control de frecuencia más estricto
    private static final String[] THROTTLED_DESTINATIONS = {
            "/app/anuncios.recientes",
            "/app/users.status.all",
            "/app/notifications/all"
    };

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

        if (accessor != null) {
            String sessionId = accessor.getSessionId();
            String destination = accessor.getDestination();

            // Generar un ID único para el mensaje
            String messageId = sessionId + "-" + accessor.getCommand() + "-" + System.currentTimeMillis();

            // Verificar si es un mensaje duplicado (dentro de un período corto)
            if (isDuplicateMessage(messageId)) {
                log.debug("Mensaje WebSocket duplicado detectado: {}", messageId);
                return message;
            }

            // Verificar si es un destino que requiere control de frecuencia
            if (StompCommand.SEND.equals(accessor.getCommand()) && destination != null) {
                // Verificar si el destino está en la lista de destinos con control de frecuencia
                for (String throttledDest : THROTTLED_DESTINATIONS) {
                    if (destination.equals(throttledDest)) {
                        // Generar un ID único para el destino
                        String destinationKey = sessionId + "-" + destination;

                        // Verificar si es una solicitud duplicada para este destino
                        if (isDuplicateDestination(destinationKey)) {
                            log.debug("Solicitud duplicada para destino controlado: {}", destination);
                            return message;
                        }
                        break;
                    }
                }
            }

            if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                // Extraer el token JWT del header
                String token = extractToken(accessor);

                if (token != null) {
                    try {
                        // Validar el token y obtener el nombre de usuario
                        String username = jwtUtil.extractUsername(token);

                        if (username != null) {
                            // Cargar los detalles del usuario
                            UserDetails userDetails = userDetailsService.loadUserByUsername(username);

                            // Validar el token
                            if (jwtUtil.validateToken(token)) {
                                // Autenticar al usuario
                                UsernamePasswordAuthenticationToken authentication =
                                        new UsernamePasswordAuthenticationToken(
                                                userDetails, null, userDetails.getAuthorities());

                                // Guardar la autenticación en el contexto de seguridad
                                SecurityContextHolder.getContext().setAuthentication(authentication);

                                // Guardar la autenticación para esta sesión
                                sessionAuthentications.put(sessionId, authentication);

                                // Obtener el ID del usuario
                                Long userId = jwtUtil.extractUserId(token);

                                if (userId != null) {
                                    // Registrar la sesión con el usuario
                                    userConnectionService.registerSession(userId, sessionId);

                                    log.info("Usuario autenticado en WebSocket: {}", userId);
                                }
                            } else {
                                log.warn("Token JWT inválido en conexión WebSocket");
                                // A pesar del error, permitimos la conexión para evitar bloqueos
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error al autenticar usuario WebSocket: {}", e.getMessage());
                        // A pesar del error, permitimos la conexión para evitar bloqueos
                        // pero sin autenticación (el usuario no podrá acceder a recursos protegidos)
                        log.warn("Permitiendo conexión WebSocket sin autenticación debido a error");
                    }
                } else {
                    // NUEVO: Mejora en el registro para depuración
                    log.warn("Intento de conexión WebSocket sin token JWT. Headers: {}",
                            accessor.getMessageHeaders());
                }
            } else if (StompCommand.DISCONNECT.equals(accessor.getCommand())) {
                // Obtener la autenticación antes de eliminarla
                Authentication authentication = sessionAuthentications.get(sessionId);

                // Verificar si hay una autenticación asociada a esta sesión
                if (authentication != null && authentication.getPrincipal() instanceof UserPrincipal userPrincipal) {
                    Long userId = userPrincipal.getId();
                    String username = userPrincipal.getUsername();

                    // Verificar si esta desconexión ya fue procesada recientemente
                    if (!isDuplicateDisconnection(userId)) {
                        log.info("Interceptando desconexión WebSocket para usuario: {} (ID: {})", username, userId);

                        try {
                            // Desconectar al usuario
                            userConnectionService.disconnectUser(userId);
                        } catch (Exception e) {
                            log.error("Error al desconectar usuario {} en interceptor: {}", userId, e.getMessage(), e);
                        }
                    } else {
                        log.debug("Desconexión duplicada detectada para usuario: {} (ID: {}). Ignorando.", username, userId);
                    }
                }

                // Eliminar la autenticación al desconectar
                sessionAuthentications.remove(sessionId);

                // Eliminar la sesión al desconectar
                if (sessionId != null) {
                    userConnectionService.removeSession(sessionId);
                }

                log.info("Sesión WebSocket {} desconectada y limpiada", sessionId);
            } else {
                // Para otros comandos (SEND, SUBSCRIBE, etc.), restaurar la autenticación si existe
                Authentication authentication = sessionAuthentications.get(sessionId);
                if (authentication != null) {
                    accessor.setUser(authentication);
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                }
            }
        }

        return message;
    }

    /**
     * Extrae el token JWT de los headers de la conexión STOMP
     */
    private String extractToken(StompHeaderAccessor accessor) {
        String authorization = accessor.getFirstNativeHeader("Authorization");

        if (authorization != null && authorization.startsWith("Bearer ")) {
            return authorization.substring(7);
        }

        // NUEVO: Log para depuración en desarrollo
        if (authorization == null) {
            log.debug("No se encontró header 'Authorization' en conexión WebSocket");
        } else if (!authorization.startsWith("Bearer ")) {
            log.debug("Header 'Authorization' encontrado pero no contiene formato Bearer: {}",
                    authorization.substring(0, Math.min(authorization.length(), 10)) + "...");
        }

        return null;
    }

    /**
     * Verifica si una sesión está activa
     * @param sessionId ID de la sesión a verificar
     * @return true si la sesión está activa, false en caso contrario
     */
    public boolean isSessionActive(String sessionId) {
        return sessionAuthentications.containsKey(sessionId);
    }

    /**
     * Verifica si un mensaje es duplicado
     */
    private boolean isDuplicateMessage(String messageId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedMessages.put(messageId, now);

        // Limpiar mensajes antiguos periódicamente (cada 100 mensajes aproximadamente)
        if (processedMessages.size() > 1000) {
            processedMessages.entrySet().removeIf(entry -> now - entry.getValue() > MESSAGE_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos de 5 segundos
        return lastTime != null && now - lastTime < MESSAGE_DEDUPLICATION_WINDOW;
    }

    /**
     * Verifica si una solicitud a un destino específico es duplicada
     * Implementa un control de frecuencia más estricto para destinos críticos
     */
    private boolean isDuplicateDestination(String destinationKey) {
        long now = System.currentTimeMillis();
        Long lastTime = processedDestinations.put(destinationKey, now);

        // Limpiar destinos antiguos periódicamente
        if (processedDestinations.size() > 100) {
            processedDestinations.entrySet().removeIf(entry -> now - entry.getValue() > DESTINATION_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de deduplicación para destinos
        return lastTime != null && now - lastTime < DESTINATION_DEDUPLICATION_WINDOW;
    }

    /**
     * Verifica si una desconexión de usuario es duplicada
     * Evita procesar múltiples desconexiones para el mismo usuario en un corto período de tiempo
     * @param userId ID del usuario que se está desconectando
     * @return true si es una desconexión duplicada, false en caso contrario
     */
    private boolean isDuplicateDisconnection(Long userId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedDisconnections.put(userId, now);

        // Limpiar desconexiones antiguas periódicamente
        if (processedDisconnections.size() > 100) {
            processedDisconnections.entrySet().removeIf(entry -> now - entry.getValue() > DISCONNECT_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de deduplicación para desconexiones
        return lastTime != null && now - lastTime < DISCONNECT_DEDUPLICATION_WINDOW;
    }

    // NUEVO: Método para limpiar periódicamente las estructuras de datos
    // Puedes llamar a este método desde un @Scheduled o similar
    public void cleanupExpiredData() {
        long now = System.currentTimeMillis();
        processedMessages.entrySet().removeIf(entry -> now - entry.getValue() > MESSAGE_DEDUPLICATION_WINDOW);
        processedDestinations.entrySet().removeIf(entry -> now - entry.getValue() > DESTINATION_DEDUPLICATION_WINDOW);
        processedDisconnections.entrySet().removeIf(entry -> now - entry.getValue() > DISCONNECT_DEDUPLICATION_WINDOW);

        log.debug("Limpieza de datos de WebSocket completada. Mensajes: {}, Destinos: {}, Desconexiones: {}",
                processedMessages.size(), processedDestinations.size(), processedDisconnections.size());
    }
}