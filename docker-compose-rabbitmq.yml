version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: midas-rabbitmq
    hostname: midas-rabbitmq
    ports:
      - "5672:5672"    # Puerto AMQP
      - "15672:15672"  # Puerto de gestión web
    environment:
      RABBITMQ_DEFAULT_USER: midasuser
      RABBITMQ_DEFAULT_PASS: MidasRabbit2025!
      RABBITMQ_DEFAULT_VHOST: /midas
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
    networks:
      - midas-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  rabbitmq_data:
    driver: local

networks:
  midas-network:
    driver: bridge
