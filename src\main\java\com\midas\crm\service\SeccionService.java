package com.midas.crm.service;

import com.midas.crm.entity.DTO.seccion.SeccionCreateDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionUpdateDTO;

import java.util.List;

public interface SeccionService {
    SeccionDTO createSeccion(SeccionCreateDTO dto);
    List<SeccionDTO> listSecciones();
    SeccionDTO getSeccionById(Long id);
    List<SeccionDTO> getSeccionesByModuloId(Long moduloId);
    SeccionDTO updateSeccion(Long id, SeccionUpdateDTO dto);
    void deleteSeccion(Long id);
}
