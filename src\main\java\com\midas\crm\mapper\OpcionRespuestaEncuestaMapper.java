package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.encuesta.OpcionRespuestaEncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.OpcionRespuestaEncuestaDTO;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;

/**
 * Mapper para convertir entre entidades OpcionRespuestaEncuesta y sus DTOs
 */
public final class OpcionRespuestaEncuestaMapper {

    private OpcionRespuestaEncuestaMapper() {}

    /**
     * Convierte un DTO de creación a una entidad OpcionRespuestaEncuesta
     */
    public static OpcionRespuestaEncuesta toEntity(OpcionRespuestaEncuestaCreateDTO dto, PreguntaEncuesta pregunta) {
        OpcionRespuestaEncuesta opcion = new OpcionRespuestaEncuesta();
        opcion.setTexto(dto.getTexto());
        opcion.setOrden(dto.getOrden());
        opcion.setValor(dto.getValor());
        opcion.setPregunta(pregunta);
        opcion.setEstado("A");
        return opcion;
    }

    /**
     * Convierte una entidad OpcionRespuestaEncuesta a un DTO
     */
    public static OpcionRespuestaEncuestaDTO toDTO(OpcionRespuestaEncuesta opcion) {
        if (opcion == null) return null;

        OpcionRespuestaEncuestaDTO dto = new OpcionRespuestaEncuestaDTO();
        dto.setId(opcion.getId());
        dto.setTexto(opcion.getTexto());
        dto.setOrden(opcion.getOrden());
        dto.setValor(opcion.getValor());
        dto.setEstado(opcion.getEstado());
        dto.setFechaCreacion(opcion.getFechaCreacion());
        dto.setFechaActualizacion(opcion.getFechaActualizacion());

        if (opcion.getPregunta() != null) {
            dto.setPreguntaId(opcion.getPregunta().getId());
        }

        return dto;
    }
}
