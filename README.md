# CRM Midas

Sistema de gestión de relaciones con clientes para Midas Solution Group.

## Requisitos

- Java 21
- MySQL 8.0 o superior
- Maven 3.9 o superior

## Configuración de entornos

El proyecto está configurado para ejecutarse en dos entornos diferentes:

- **Desarrollo (dev)**: Para desarrollo en máquina local
- **Producción (prod)**: Para el entorno de producción

### Archivos de configuración

- `application.properties`: Configuración base común para todos los entornos
- `application-dev.properties`: Configuración específica para entorno de desarrollo
- `application-prod.properties`: Configuración específica para entorno de producción

## Ejecución de la aplicación

### Entorno de Desarrollo (por defecto)

Para ejecutar la aplicación en entorno de desarrollo:

```bash
# Usando Maven
./mvnw spring-boot:run

# O con el JAR empaquetado
java -jar target/crm-0.0.1-SNAPSHOT.jar
```

### Entorno de Producción

Para ejecutar la aplicación en entorno de producción:

```bash
# Usando Maven
./mvnw spring-boot:run -Dspring.profiles.active=prod

# O con el JAR empaquetado
java -jar -Dspring.profiles.active=prod target/crm-0.0.1-SNAPSHOT.jar
```

## Documentación de la API

La documentación de la API está disponible en:

- Entorno de Desarrollo: http://localhost:9039/swagger-ui.html
- Entorno de Producción: https://[tu-dominio]/swagger-ui.html

## Base de datos

### Entorno de Desarrollo
- URL: ***************************************
- Usuario: root
- Contraseña: (vacía)

### Entorno de Producción
- URL: jdbc:mysql://*************:3306/midascrm_db
- Usuario: user
- Contraseña: Midas*2025$

## Contacto

Para más información, contactar a:
- Email: <EMAIL>
- Web: https://www.midassolutiongroup.com
