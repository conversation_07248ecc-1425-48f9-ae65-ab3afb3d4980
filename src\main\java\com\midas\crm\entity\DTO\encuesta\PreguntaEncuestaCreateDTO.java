package com.midas.crm.entity.DTO.encuesta;

import com.midas.crm.entity.PreguntaEncuesta;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO para crear una nueva pregunta de encuesta
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaEncuestaCreateDTO {
    
    @NotBlank(message = "El enunciado es obligatorio")
    private String enunciado;
    
    private String descripcion;
    
    private Integer orden;
    
    @NotNull(message = "El tipo de pregunta es obligatorio")
    private PreguntaEncuesta.TipoPregunta tipo = PreguntaEncuesta.TipoPregunta.OPCION_MULTIPLE;
    
    private Boolean esObligatoria = true;
    
    private Long encuestaId;
    
    private List<OpcionRespuestaEncuestaCreateDTO> opciones;
}
