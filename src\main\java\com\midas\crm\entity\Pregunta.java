package com.midas.crm.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Entidad que representa una pregunta de un cuestionario
 */
@Entity
@Table(name = "preguntas")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Pregunta {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String enunciado;

    @Column(length = 500)
    private String explicacion; // Explicación que se muestra después de responder

    @Column(nullable = false)
    private Integer puntaje = 1; // Valor de la pregunta en puntos

    @Column(nullable = false)
    private Integer orden; // Orden de la pregunta en el cuestionario

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TipoPregunta tipo = TipoPregunta.OPCION_MULTIPLE; // Tipo de pregunta

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cuestionario_id", nullable = false)
    private Cuestionario cuestionario;

    @OneToMany(mappedBy = "pregunta", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Respuesta> respuestas = new ArrayList<>();

    @Column(nullable = false, length = 1)
    private String estado = "A"; // A: Activo, I: Inactivo

    @CreationTimestamp
    @Column(name = "fecha_creacion", updatable = false)
    private LocalDateTime fechaCreacion;

    @UpdateTimestamp
    @Column(name = "fecha_actualizacion")
    private LocalDateTime fechaActualizacion;

    /**
     * Enum que define los tipos de preguntas disponibles
     */
    public enum TipoPregunta {
        OPCION_MULTIPLE,    // Una sola respuesta correcta
        SELECCION_MULTIPLE, // Múltiples respuestas correctas
        VERDADERO_FALSO,    // Pregunta de verdadero o falso
        TEXTO_LIBRE         // Respuesta de texto libre
    }
}
