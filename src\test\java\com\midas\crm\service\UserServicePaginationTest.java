package com.midas.crm.service;

import com.midas.crm.entity.DTO.user.UserPageDTO;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test para verificar que la lógica de paginación funciona correctamente
 */
public class UserServicePaginationTest {

    @Test
    public void testPaginationCalculation() {
        // Test para verificar que el cálculo de paginación es correcto

        // Caso 1: 400 usuarios, 10 por página = 40 páginas
        long totalItems = 400;
        int size = 10;
        int expectedTotalPages = (int) Math.ceil((double) totalItems / size);

        assertEquals(40, expectedTotalPages,
                "400 usuarios con 10 por página debería dar 40 páginas");

        // Caso 2: 395 usuarios, 10 por página = 40 páginas (39.5 redondeado hacia
        // arriba)
        totalItems = 395;
        expectedTotalPages = (int) Math.ceil((double) totalItems / size);

        assertEquals(40, expectedTotalPages,
                "395 usuarios con 10 por página debería dar 40 páginas");

        // Caso 3: 390 usuarios, 10 por página = 39 páginas
        totalItems = 390;
        expectedTotalPages = (int) Math.ceil((double) totalItems / size);

        assertEquals(39, expectedTotalPages,
                "390 usuarios con 10 por página debería dar 39 páginas");

        System.out.println("✅ Cálculos de paginación correctos");
    }

    @Test
    public void testUserPageDTOStructure() {
        // Test para verificar que UserPageDTO tiene la estructura correcta
        UserPageDTO pageDTO = new UserPageDTO();

        // Verificar que se puede crear y configurar correctamente
        pageDTO.setCurrentPage(0);
        pageDTO.setTotalItems(400L);
        pageDTO.setTotalPages(40);

        assertEquals(0, pageDTO.getCurrentPage());
        assertEquals(400L, pageDTO.getTotalItems());
        assertEquals(40, pageDTO.getTotalPages());

        System.out.println("✅ Estructura UserPageDTO correcta");
    }
}
