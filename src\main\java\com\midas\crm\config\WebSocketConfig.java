package com.midas.crm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker("/topic", "/queue", "/user")
                .setHeartbeatValue(new long[] { 20000, 20000 }) // Heartbeat cada 20 segundos
                .setTaskScheduler(taskScheduler()); // Usar scheduler personalizado
        config.setApplicationDestinationPrefixes("/app");
        config.setUserDestinationPrefix("/user");
    }

    /**
     * Configurar un TaskScheduler personalizado para el message broker
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(10);
        scheduler.setThreadNamePrefix("websocket-heartbeat-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(30);
        return scheduler;
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // Usar setAllowedOriginPatterns en lugar de setAllowedOrigins para mejor
        // compatibilidad
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*") // Más permisivo para pruebas
                /*
                 * Para producción, considera usar una lista específica:
                 * .setAllowedOriginPatterns(
                 * "http://localhost:*",
                 * "https://*.midassolutiongroup.com",
                 * "https://apisozarusac.com",
                 * "https://apisozarusac.com/BackendJava",
                 * "https://apisozarusac.com/BackendJava/*"
                 * )
                 */
                .withSockJS()
                .setDisconnectDelay(60_000) // 1 min (reducido para detectar desconexiones más rápido)
                .setHeartbeatTime(20000) // 20 segundos (sincronizado con el message broker)
                .setSessionCookieNeeded(false) // No requerir cookies para mejor compatibilidad
                .setStreamBytesLimit(128 * 1024) // 128KB límite de stream
                .setHttpMessageCacheSize(1000) // Cache de mensajes HTTP
                .setClientLibraryUrl("https://cdn.jsdelivr.net/npm/sockjs-client@1.6.1/dist/sockjs.min.js");

        // Agregar un segundo endpoint sin SockJS para clientes nativos WebSocket
        registry.addEndpoint("/ws")
                .setAllowedOriginPatterns("*");
    }
}