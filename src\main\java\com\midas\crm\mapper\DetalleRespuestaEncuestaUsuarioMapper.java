package com.midas.crm.mapper;

import com.midas.crm.entity.DetalleRespuestaEncuestaUsuario;
import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.encuesta.DetalleRespuestaEncuestaUsuarioDTO;
import com.midas.crm.entity.OpcionRespuestaEncuesta;
import com.midas.crm.entity.PreguntaEncuesta;
import com.midas.crm.entity.RespuestaEncuestaUsuario;

/**
 * Mapper para convertir entre entidades DetalleRespuestaEncuestaUsuario y sus DTOs
 */
public final class DetalleRespuestaEncuestaUsuarioMapper {

    private DetalleRespuestaEncuestaUsuarioMapper() {}

    /**
     * Convierte un DTO de creación a una entidad DetalleRespuestaEncuestaUsuario
     */
    public static DetalleRespuestaEncuestaUsuario toEntity(DetalleRespuestaEncuestaUsuarioCreateDTO dto, 
                                                          RespuestaEncuestaUsuario respuestaEncuestaUsuario,
                                                          PreguntaEncuesta pregunta,
                                                          OpcionRespuestaEncuesta opcion) {
        DetalleRespuestaEncuestaUsuario detalle = new DetalleRespuestaEncuestaUsuario();
        detalle.setRespuestaEncuestaUsuario(respuestaEncuestaUsuario);
        detalle.setPregunta(pregunta);
        detalle.setOpcion(opcion); // Puede ser null para preguntas de texto libre, fecha o número
        detalle.setRespuestaTexto(dto.getRespuestaTexto());
        detalle.setRespuestaNumero(dto.getRespuestaNumero());
        detalle.setRespuestaFecha(dto.getRespuestaFecha());
        return detalle;
    }

    /**
     * Convierte una entidad DetalleRespuestaEncuestaUsuario a un DTO
     */
    public static DetalleRespuestaEncuestaUsuarioDTO toDTO(DetalleRespuestaEncuestaUsuario detalle) {
        if (detalle == null) return null;

        DetalleRespuestaEncuestaUsuarioDTO dto = new DetalleRespuestaEncuestaUsuarioDTO();
        dto.setId(detalle.getId());
        dto.setRespuestaTexto(detalle.getRespuestaTexto());
        dto.setRespuestaNumero(detalle.getRespuestaNumero());
        dto.setRespuestaFecha(detalle.getRespuestaFecha());
        dto.setFechaCreacion(detalle.getFechaCreacion());
        dto.setFechaActualizacion(detalle.getFechaActualizacion());

        // Información de la respuesta de usuario
        if (detalle.getRespuestaEncuestaUsuario() != null) {
            dto.setRespuestaEncuestaUsuarioId(detalle.getRespuestaEncuestaUsuario().getId());
        }

        // Información de la pregunta
        if (detalle.getPregunta() != null) {
            dto.setPreguntaId(detalle.getPregunta().getId());
            dto.setPreguntaEnunciado(detalle.getPregunta().getEnunciado());
            dto.setPreguntaTipo(detalle.getPregunta().getTipo().name());
        }

        // Información de la opción (si aplica)
        if (detalle.getOpcion() != null) {
            dto.setOpcionId(detalle.getOpcion().getId());
            dto.setOpcionTexto(detalle.getOpcion().getTexto());
        }

        return dto;
    }
}
