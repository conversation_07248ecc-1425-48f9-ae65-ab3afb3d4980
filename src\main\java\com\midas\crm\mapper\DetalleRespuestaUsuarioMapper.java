package com.midas.crm.mapper;

import com.midas.crm.entity.DetalleRespuestaUsuario;
import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioDTO;
import com.midas.crm.entity.Pregunta;
import com.midas.crm.entity.Respuesta;
import com.midas.crm.entity.RespuestaUsuario;

public final class DetalleRespuestaUsuarioMapper {

    private DetalleRespuestaUsuarioMapper() {}

    public static DetalleRespuestaUsuario toEntity(DetalleRespuestaUsuarioCreateDTO dto, RespuestaUsuario respuestaUsuario, Pregunta pregunta, Respuesta respuesta) {
        DetalleRespuestaUsuario detalle = new DetalleRespuestaUsuario();
        detalle.setRespuestaUsuario(respuestaUsuario);
        detalle.setPregunta(pregunta);
        detalle.setRespuesta(respuesta);
        detalle.setTextoRespuesta(dto.getTextoRespuesta());
        
        // Determinar si la respuesta es correcta
        if (respuesta != null) {
            detalle.setEsCorrecta(respuesta.getEsCorrecta());
            detalle.setPuntajeObtenido(respuesta.getEsCorrecta() ? pregunta.getPuntaje() : 0);
        } else {
            detalle.setEsCorrecta(false);
            detalle.setPuntajeObtenido(0);
        }
        
        return detalle;
    }

    public static DetalleRespuestaUsuarioDTO toDTO(DetalleRespuestaUsuario detalle) {
        if (detalle == null) return null;

        return new DetalleRespuestaUsuarioDTO(
                detalle.getId(),
                detalle.getRespuestaUsuario() != null ? detalle.getRespuestaUsuario().getId() : null,
                detalle.getPregunta() != null ? detalle.getPregunta().getId() : null,
                detalle.getPregunta() != null ? detalle.getPregunta().getEnunciado() : null,
                detalle.getRespuesta() != null ? detalle.getRespuesta().getId() : null,
                detalle.getRespuesta() != null ? detalle.getRespuesta().getTexto() : null,
                detalle.getTextoRespuesta(),
                detalle.getEsCorrecta(),
                detalle.getPuntajeObtenido(),
                detalle.getFechaCreacion(),
                detalle.getFechaActualizacion()
        );
    }
}
