package com.midas.crm.controller;

import com.midas.crm.entity.DTO.NotificationDTO;
import com.midas.crm.entity.DTO.NotificationReadUserDTO;
import com.midas.crm.entity.Notification.NotificationCategory;
import com.midas.crm.entity.Notification.NotificationType;
import com.midas.crm.service.serviceImpl.NotificationService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Controlador para gestionar las notificaciones
 */
@RestController
@RequestMapping("${api.route.notifications}")
@RequiredArgsConstructor
@Slf4j
public class NotificationController {

    private final NotificationService notificationService;
    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Crea una nueva notificación
     *
     * @param notificationDTO DTO con los datos de la notificación
     * @return Respuesta con la notificación creada
     */
    @PostMapping
    public ResponseEntity<GenericResponse<NotificationDTO>> createNotification(
            @Valid @RequestBody NotificationDTO notificationDTO) {
        NotificationDTO createdNotification = notificationService.createNotification(notificationDTO);
        return ResponseEntity.ok(new GenericResponse<>(1, "Notificación creada exitosamente", createdNotification));
    }

    /**
     * Obtiene las notificaciones para un usuario
     *
     * @param userId ID del usuario
     * @param page   Número de página (opcional, por defecto 0)
     * @param size   Tamaño de página (opcional, por defecto 10)
     * @return Respuesta con la página de notificaciones
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<GenericResponse<Page<NotificationDTO>>> getNotificationsForUser(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        Page<NotificationDTO> notifications = notificationService.getNotificationsForUser(userId, page, size);
        return ResponseEntity.ok(new GenericResponse<>(1, "Notificaciones obtenidas exitosamente", notifications));
    }

    /**
     * Marca una notificación como leída
     *
     * @param notificationId ID de la notificación
     * @param userId         ID del usuario
     * @return Respuesta con el resultado de la operación
     */
    @PostMapping("/{notificationId}/read")
    public ResponseEntity<GenericResponse<Boolean>> markAsRead(
            @PathVariable Long notificationId,
            @RequestParam Long userId) {

        boolean success = notificationService.markAsRead(notificationId, userId);

        if (success) {
            return ResponseEntity.ok(new GenericResponse<>(1, "Notificación marcada como leída", true));
        } else {
            return ResponseEntity.ok(new GenericResponse<>(0, "No se pudo marcar la notificación como leída", false));
        }
    }

    /**
     * Obtiene el número de notificaciones no leídas para un usuario
     *
     * @param userId ID del usuario
     * @return Respuesta con el número de notificaciones no leídas
     */
    @GetMapping("/unread-count/{userId}")
    public ResponseEntity<GenericResponse<Long>> getUnreadCount(@PathVariable Long userId) {
        long count = notificationService.getUnreadCount(userId);
        return ResponseEntity.ok(new GenericResponse<>(1, "Conteo de notificaciones no leídas", count));
    }

    /**
     * Obtiene las últimas notificaciones para un usuario
     *
     * @param userId ID del usuario
     * @param limit  Número máximo de notificaciones (opcional, por defecto 5)
     * @return Respuesta con la lista de notificaciones
     */
    @GetMapping("/latest/{userId}")
    public ResponseEntity<GenericResponse<List<NotificationDTO>>> getLatestNotifications(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "5") int limit) {

        List<NotificationDTO> notifications = notificationService.getLatestNotifications(userId, limit);
        return ResponseEntity
                .ok(new GenericResponse<>(1, "Últimas notificaciones obtenidas exitosamente", notifications));
    }

    /**
     * Endpoint para polling HTTP de notificaciones
     * Útil como fallback cuando WebSocket no está disponible
     *
     * @param userId ID del usuario
     * @return Respuesta con datos de notificaciones
     */
    @GetMapping("/polling/{userId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> pollingNotifications(@PathVariable Long userId) {
        Map<String, Object> data = new HashMap<>();
        data.put("unreadCount", notificationService.getUnreadCount(userId));
        data.put("latestNotifications", notificationService.getLatestNotifications(userId, 5));

        return ResponseEntity.ok(new GenericResponse<>(1, "Datos de notificaciones obtenidos", data));
    }

    /**
     * Obtiene la lista de usuarios que han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Respuesta con la lista de usuarios que han leído la notificación
     */
    @GetMapping("/{notificationId}/readers")
    public ResponseEntity<GenericResponse<List<NotificationReadUserDTO>>> getNotificationReaders(
            @PathVariable Long notificationId) {
        List<NotificationReadUserDTO> readers = notificationService.getUsersWhoReadNotification(notificationId);
        return ResponseEntity
                .ok(new GenericResponse<>(1, "Usuarios que han leído la notificación obtenidos exitosamente", readers));
    }

    /**
     * Obtiene el número de usuarios que han leído una notificación específica
     *
     * @param notificationId ID de la notificación
     * @return Respuesta con el número de usuarios que han leído la notificación
     */
    @GetMapping("/{notificationId}/read-count")
    public ResponseEntity<GenericResponse<Long>> getNotificationReadCount(
            @PathVariable Long notificationId) {
        long count = notificationService.getReadCount(notificationId);
        return ResponseEntity.ok(new GenericResponse<>(1, "Número de lecturas obtenido exitosamente", count));
    }

    // ==================== WebSocket Endpoints ====================

    /**
     * Endpoint WebSocket para enviar una notificación broadcast
     *
     * @param notificationDTO DTO con los datos de la notificación
     * @return La notificación creada
     */
    @MessageMapping("/notifications.send")
    @SendTo("/topic/notifications")
    public NotificationDTO sendNotification(@Payload NotificationDTO notificationDTO) {
        return notificationService.createNotification(notificationDTO);
    }

    /**
     * Endpoint WebSocket para crear cualquier tipo de notificación
     * Este endpoint maneja tanto notificaciones broadcast como personales
     *
     * @param notificationDTO DTO con los datos de la notificación
     */
    @MessageMapping("/notifications.create")
    public void createNotificationWs(@Payload NotificationDTO notificationDTO) {
        try {
            log.info("Creando notificación por WebSocket: {}", notificationDTO.getMessage());

            // Crear la notificación
            NotificationDTO createdNotification = notificationService.createNotification(notificationDTO);

            // Si es una notificación broadcast, ya se envía automáticamente en el servicio

            // Si es una notificación personal, asegurarse de que se envíe al destinatario
            if (notificationDTO.getRecipientId() != null) {
                // Enviar explícitamente al destinatario
                messagingTemplate.convertAndSendToUser(
                        notificationDTO.getRecipientId().toString(),
                        "/queue/notifications",
                        createdNotification);

                // Actualizar el contador de no leídas
                long count = notificationService.getUnreadCount(notificationDTO.getRecipientId());
                messagingTemplate.convertAndSendToUser(
                        notificationDTO.getRecipientId().toString(),
                        "/queue/notifications.count",
                        count);

                log.info("Notificación personal enviada al usuario {} por WebSocket", notificationDTO.getRecipientId());
            }
        } catch (Exception e) {
            log.error("Error al crear notificación por WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Endpoint WebSocket para marcar una notificación como leída
     *
     * @param payload Mapa con los datos de la notificación y el usuario
     * @return Resultado de la operación
     */
    @MessageMapping("/notifications.markAsRead")
    public void markNotificationAsRead(@Payload Map<String, Long> payload) {
        Long notificationId = payload.get("notificationId");
        Long userId = payload.get("userId");

        if (notificationId != null && userId != null) {
            // Obtener el contador antes de marcar como leída
            long previousCount = notificationService.getUnreadCount(userId);

            // Marcar como leída
            boolean success = notificationService.markAsRead(notificationId, userId);

            // Notificar al usuario sobre el resultado
            messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications.read",
                    Map.of("success", success, "notificationId", notificationId));

            // Obtener el nuevo contador después de marcar como leída
            long newUnreadCount = notificationService.getUnreadCount(userId);

            // Solo enviar actualización si el contador ha cambiado
            if (previousCount != newUnreadCount) {
                messagingTemplate.convertAndSendToUser(
                        userId.toString(),
                        "/queue/notifications.count",
                        newUnreadCount);
            }

            // Si la notificación se marcó como leída exitosamente, notificar a todos los
            // usuarios
            // sobre la actualización de lecturas para esta notificación
            if (success) {
                List<NotificationReadUserDTO> readers = notificationService.getUsersWhoReadNotification(notificationId);
                long readCount = notificationService.getReadCount(notificationId);

                // Enviar actualización de lecturas a todos los usuarios conectados
                messagingTemplate.convertAndSend("/topic/notifications/" + notificationId + "/readers",
                        Map.of("readers", readers, "readCount", readCount, "notificationId", notificationId));
            }
        }
    }

    /**
     * Endpoint WebSocket para obtener el conteo de notificaciones no leídas
     *
     * @param userId ID del usuario
     * @return Número de notificaciones no leídas
     */
    @MessageMapping("/notifications.unreadCount")
    public void getUnreadCountWs(@Payload Long userId) {
        if (userId != null) {
            long count = notificationService.getUnreadCount(userId);
            messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications.count",
                    count);
        }
    }

    /**
     * Endpoint WebSocket para enviar una notificación a un usuario específico
     *
     * @param payload Mapa con los datos de la notificación y el usuario
     */
    @MessageMapping("/notifications.sendToUser")
    public void sendNotificationToUser(@Payload Map<String, Object> payload) {
        try {
            // Extraer el ID del usuario y la notificación del payload
            Long userId = Long.valueOf(payload.get("userId").toString());
            @SuppressWarnings("unchecked")
            Map<String, Object> notificationMap = (Map<String, Object>) payload.get("notification");

            // Convertir el mapa a un DTO
            NotificationDTO notificationDTO = new NotificationDTO();
            notificationDTO.setId(Long.valueOf(notificationMap.get("id").toString()));
            notificationDTO.setRecipientId(userId);
            notificationDTO.setSenderId(
                    notificationMap.containsKey("senderId") ? Long.valueOf(notificationMap.get("senderId").toString())
                            : null);
            notificationDTO.setSenderName((String) notificationMap.get("senderName"));
            notificationDTO.setMessage((String) notificationMap.get("message"));
            notificationDTO.setType(NotificationType.valueOf((String) notificationMap.get("type")));
            notificationDTO.setCategory(NotificationCategory.valueOf((String) notificationMap.get("category")));
            notificationDTO.setRead(Boolean.FALSE);

            // Enviar la notificación directamente al usuario
            if (userId != null) {
                messagingTemplate.convertAndSendToUser(
                        userId.toString(),
                        "/queue/notifications",
                        notificationDTO);

                // Actualizar el contador de no leídas
                long count = notificationService.getUnreadCount(userId);
                messagingTemplate.convertAndSendToUser(
                        userId.toString(),
                        "/queue/notifications.count",
                        count);

                log.info("Notificación enviada directamente al usuario {} por WebSocket", userId);
            }
        } catch (Exception e) {
            log.error("Error al enviar notificación a usuario por WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Endpoint WebSocket para obtener todas las notificaciones
     *
     * @param payload Mapa con los datos de la solicitud
     */
    @MessageMapping("/notifications/all")
    public void getAllNotificationsWs(@Payload Map<String, Object> payload) {
        try {
            Long userId = Long.valueOf(payload.get("userId").toString());
            Integer limit = payload.containsKey("limit") ? Integer.valueOf(payload.get("limit").toString()) : 5;

            log.info("Solicitando notificaciones para usuario {} con límite {}", userId, limit);

            // Obtener las notificaciones
            List<NotificationDTO> notifications = notificationService.getLatestNotifications(userId, limit);

            // Enviar las notificaciones al usuario
            messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications.all",
                    notifications);

            log.info("Enviadas {} notificaciones al usuario {} por WebSocket", notifications.size(), userId);

            // También enviar el contador de no leídas
            long unreadCount = notificationService.getUnreadCount(userId);
            messagingTemplate.convertAndSendToUser(
                    userId.toString(),
                    "/queue/notifications.count",
                    unreadCount);

            log.info("Enviado contador de no leídas ({}) al usuario {} por WebSocket", unreadCount, userId);
        } catch (Exception e) {
            log.error("Error al obtener notificaciones por WebSocket: {}", e.getMessage(), e);
        }
    }

    /**
     * Envía notificaciones a usuarios por rol específico
     */
    @PostMapping("/enviar-por-rol")
    public ResponseEntity<GenericResponse<String>> sendNotificationByRole(
            @RequestBody Map<String, Object> request) {
        try {
            String role = (String) request.get("role");
            String title = (String) request.get("title");
            String message = (String) request.get("message");
            String senderName = (String) request.get("senderName");

            if (role == null || title == null || message == null) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Rol, título y mensaje son obligatorios",
                                null));
            }

            int notificationsSent = notificationService.sendNotificationByRole(role, title, message, senderName);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Notificaciones enviadas exitosamente a " + notificationsSent + " usuarios con rol " + role,
                    "Enviadas: " + notificationsSent));
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por rol: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al enviar notificaciones por rol: " + e.getMessage(),
                            null));
        }
    }

    /**
     * Envía notificaciones a usuarios por sede específica
     */
    @PostMapping("/enviar-por-sede")
    public ResponseEntity<GenericResponse<String>> sendNotificationBySede(
            @RequestBody Map<String, Object> request) {
        try {
            Long sedeId = Long.valueOf(request.get("sedeId").toString());
            String title = (String) request.get("title");
            String message = (String) request.get("message");
            String senderName = (String) request.get("senderName");

            if (sedeId == null || title == null || message == null) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Sede, título y mensaje son obligatorios",
                                null));
            }

            int notificationsSent = notificationService.sendNotificationBySede(sedeId, title, message, senderName);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Notificaciones enviadas exitosamente a " + notificationsSent + " usuarios de la sede",
                    "Enviadas: " + notificationsSent));
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por sede: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al enviar notificaciones por sede: " + e.getMessage(),
                            null));
        }
    }

    /**
     * Envía notificaciones a usuarios por sede y rol específicos
     */
    @PostMapping("/enviar-por-sede-rol")
    public ResponseEntity<GenericResponse<String>> sendNotificationBySedeAndRole(
            @RequestBody Map<String, Object> request) {
        try {
            Long sedeId = Long.valueOf(request.get("sedeId").toString());
            String role = (String) request.get("role");
            String title = (String) request.get("title");
            String message = (String) request.get("message");
            String senderName = (String) request.get("senderName");

            if (sedeId == null || role == null || title == null || message == null) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Sede, rol, título y mensaje son obligatorios",
                                null));
            }

            int notificationsSent = notificationService.sendNotificationBySedeAndRole(sedeId, role, title, message,
                    senderName);

            return ResponseEntity.ok(new GenericResponse<>(
                    GenericResponseConstants.SUCCESS,
                    "Notificaciones enviadas exitosamente a " + notificationsSent + " usuarios con rol " + role
                            + " de la sede",
                    "Enviadas: " + notificationsSent));
        } catch (Exception e) {
            log.error("Error al enviar notificaciones por sede y rol: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error al enviar notificaciones por sede y rol: " + e.getMessage(),
                            null));
        }
    }
}
