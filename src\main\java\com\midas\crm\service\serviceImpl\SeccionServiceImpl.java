package com.midas.crm.service.serviceImpl;

import com.midas.crm.entity.DTO.seccion.SeccionCreateDTO;
import com.midas.crm.entity.DTO.seccion.SeccionDTO;
import com.midas.crm.entity.DTO.seccion.SeccionUpdateDTO;
import com.midas.crm.entity.Modulo;
import com.midas.crm.entity.Seccion;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.mapper.SeccionMapper;
import com.midas.crm.repository.ModuloRepository;
import com.midas.crm.repository.SeccionRepository;
import com.midas.crm.service.SeccionService;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SeccionServiceImpl implements SeccionService {

    private final SeccionRepository seccionRepository;
    private final ModuloRepository moduloRepository;

    @Override
    @Transactional
    public SeccionDTO createSeccion(SeccionCreateDTO dto) {
        // Verificar si ya existe una sección con el mismo título en el mismo módulo
        if (seccionRepository.existsByTituloAndModuloId(dto.getTitulo(), dto.getModuloId())) {
            throw new MidasExceptions(MidasErrorMessage.SECCION_ALREADY_EXISTS);
        }

        // Obtener el módulo
        Modulo modulo = moduloRepository.findById(dto.getModuloId())
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.MODULO_NOT_FOUND));

        // Crear la sección
        Seccion seccion = SeccionMapper.toEntity(dto, modulo);

        // Si no se especifica un orden, asignar el siguiente
        if (seccion.getOrden() == null) {
            List<Seccion> secciones = seccionRepository.findByModuloIdOrderByOrdenAsc(modulo.getId());
            seccion.setOrden(secciones.isEmpty() ? 1 : secciones.get(secciones.size() - 1).getOrden() + 1);
        }

        return SeccionMapper.toDTO(seccionRepository.save(seccion));
    }

    @Override
    @Transactional(readOnly = true)
    public List<SeccionDTO> listSecciones() {
        return seccionRepository.findAll().stream()
                .map(SeccionMapper::toDTOWithoutLecciones)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SeccionDTO getSeccionById(Long id) {
        return seccionRepository.findById(id)
                .map(SeccionMapper::toDTO)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SECCION_NOT_FOUND));
    }

    @Override
    @Transactional(readOnly = true)
    public List<SeccionDTO> getSeccionesByModuloId(Long moduloId) {
        return seccionRepository.findByModuloIdOrderByOrdenAsc(moduloId).stream()
                .map(SeccionMapper::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public SeccionDTO updateSeccion(Long id, SeccionUpdateDTO dto) {
        Seccion seccion = seccionRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SECCION_NOT_FOUND));

        SeccionMapper.updateEntity(seccion, dto);
        return SeccionMapper.toDTO(seccionRepository.save(seccion));
    }

    @Override
    @Transactional
    public void deleteSeccion(Long id) {
        Seccion seccion = seccionRepository.findById(id)
                .orElseThrow(() -> new MidasExceptions(MidasErrorMessage.SECCION_NOT_FOUND));

        // Marcar como inactivo en lugar de eliminar físicamente
        seccion.setEstado("I");
        seccionRepository.save(seccion);
    }
}
