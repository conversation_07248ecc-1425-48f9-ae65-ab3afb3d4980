package com.midas.crm.controller;

import com.midas.crm.entity.DTO.cuestionario.DetalleRespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioCreateDTO;
import com.midas.crm.entity.DTO.cuestionario.RespuestaUsuarioDTO;
import com.midas.crm.service.RespuestaUsuarioService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("${api.route.respuesta-usuario}")
@RequiredArgsConstructor
@Slf4j
public class RespuestaUsuarioController {

    private final RespuestaUsuarioService respuestaUsuarioService;

    /**
     * Inicia un nuevo intento de cuestionario
     * Implementado con programación funcional
     */
    @PostMapping("/iniciar")
    public ResponseEntity<GenericResponse<RespuestaUsuarioDTO>> iniciarCuestionario(@Valid @RequestBody RespuestaUsuarioCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(respuestaUsuarioService::iniciarCuestionario)
            .map(respuestaUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario iniciado exitosamente", respuestaUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Responde una pregunta del cuestionario
     * Implementado con programación funcional
     */
    @PostMapping("/{respuestaUsuarioId}/responder")
    public ResponseEntity<GenericResponse<RespuestaUsuarioDTO>> responderPregunta(
            @PathVariable Long respuestaUsuarioId,
            @Valid @RequestBody DetalleRespuestaUsuarioCreateDTO dto) {
        return Optional.ofNullable(dto)
            .map(detalleDto -> respuestaUsuarioService.responderPregunta(respuestaUsuarioId, detalleDto))
            .map(respuestaUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Pregunta respondida exitosamente", respuestaUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Finaliza un intento de cuestionario
     * Implementado con programación funcional
     */
    @PostMapping("/{respuestaUsuarioId}/finalizar")
    public ResponseEntity<GenericResponse<RespuestaUsuarioDTO>> finalizarCuestionario(@PathVariable Long respuestaUsuarioId) {
        return Optional.ofNullable(respuestaUsuarioId)
            .map(respuestaUsuarioService::finalizarCuestionario)
            .map(respuestaUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Cuestionario finalizado exitosamente", respuestaUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene una respuesta de usuario por su ID
     * Implementado con programación funcional
     */
    @GetMapping("/{id}")
    public ResponseEntity<GenericResponse<RespuestaUsuarioDTO>> getRespuestaUsuario(@PathVariable Long id) {
        return Optional.ofNullable(id)
            .map(respuestaUsuarioService::getRespuestaUsuarioById)
            .map(respuestaUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuesta de usuario encontrada", respuestaUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene las respuestas de un usuario para un cuestionario
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/cuestionario/{cuestionarioId}")
    public ResponseEntity<GenericResponse<List<RespuestaUsuarioDTO>>> getRespuestaUsuarioByUsuarioAndCuestionario(
            @PathVariable Long usuarioId,
            @PathVariable Long cuestionarioId) {
        return Optional.of(new Long[]{usuarioId, cuestionarioId})
            .map(ids -> respuestaUsuarioService.getRespuestaUsuarioByUsuarioAndCuestionario(ids[0], ids[1]))
            .map(respuestasUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Respuestas de usuario por cuestionario", respuestasUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene el mejor intento de un usuario para un cuestionario
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/cuestionario/{cuestionarioId}/mejor")
    public ResponseEntity<GenericResponse<RespuestaUsuarioDTO>> getMejorIntentoByUsuarioAndCuestionario(
            @PathVariable Long usuarioId,
            @PathVariable Long cuestionarioId) {
        return Optional.of(new Long[]{usuarioId, cuestionarioId})
            .map(ids -> respuestaUsuarioService.getMejorIntentoByUsuarioAndCuestionario(ids[0], ids[1]))
            .map(respuestaUsuario -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Mejor intento del usuario", respuestaUsuario)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Obtiene el resumen de cuestionarios por curso y usuario
     * Implementado con programación funcional
     */
    @GetMapping("/resumen/curso/{cursoId}/usuario/{usuarioId}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> getResumenCuestionariosByCursoAndUsuario(
            @PathVariable Long cursoId,
            @PathVariable Long usuarioId) {
        return Optional.of(new Long[]{cursoId, usuarioId})
            .map(ids -> respuestaUsuarioService.getResumenCuestionariosByCursoAndUsuario(ids[0], ids[1]))
            .map(resumen -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Resumen de cuestionarios del curso", resumen)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }

    /**
     * Verifica si un usuario puede intentar un cuestionario
     * Implementado con programación funcional
     */
    @GetMapping("/usuario/{usuarioId}/cuestionario/{cuestionarioId}/puede-intentar")
    public ResponseEntity<GenericResponse<Boolean>> puedeIntentarCuestionario(
            @PathVariable Long usuarioId,
            @PathVariable Long cuestionarioId) {
        return Optional.of(new Long[]{usuarioId, cuestionarioId})
            .map(ids -> respuestaUsuarioService.puedeIntentarCuestionario(ids[0], ids[1]))
            .map(puedeIntentar -> ResponseEntity.ok(
                new GenericResponse<>(GenericResponseConstants.SUCCESS, "Verificación de intentos", puedeIntentar)
            ))
            .orElse(ResponseEntity.badRequest().build());
    }
}
