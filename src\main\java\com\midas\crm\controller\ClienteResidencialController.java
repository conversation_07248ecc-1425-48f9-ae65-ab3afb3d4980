package com.midas.crm.controller;

import com.midas.crm.entity.ClienteResidencial;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.service.ClienteResidencialExcelService;
import com.midas.crm.service.ClienteResidencialService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

@RestController
@RequestMapping("${api.route.clientes}")
@RequiredArgsConstructor
public class ClienteResidencialController {

    private final ClienteResidencialService clienteResidencialService;
    private final ClienteResidencialExcelService clienteResidencialExcelService;

    /**
     * Función utilitaria para manejar excepciones de forma funcional
     */
    private final Function<Throwable, ClienteResidencial> manejarExcepcionCliente = ex -> {
        if (ex instanceof NoSuchElementException) {
            throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_NOT_FOUND);
        } else if (!(ex instanceof MidasExceptions)) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
        throw (MidasExceptions) ex;
    };

    /**
     * Función utilitaria para manejar excepciones en listas de clientes
     */
    private final Function<Throwable, List<ClienteResidencial>> manejarExcepcionLista = ex -> {
        if (ex instanceof NoSuchElementException) {
            throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_NOT_FOUND);
        } else if (!(ex instanceof MidasExceptions)) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
        throw (MidasExceptions) ex;
    };

    /**
     * Endpoint para obtener todos los clientes con usuario (sin filtros)
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono
     */
    @GetMapping("/con-usuario")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuario(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {

        // Usar CompletableFuture con programación funcional
        return CompletableFuture
                .supplyAsync(() -> clienteResidencialService.obtenerClientesConUsuario(page, size))
                .exceptionally(ex -> {
                    // Manejar excepciones de forma funcional
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Error al obtener clientes: " + ex.getMessage(),
                                    null));
                })
                .join();
    }

    /**
     * Endpoint para obtener clientes filtrados por fecha actual
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono
     */
    @GetMapping("/con-usuario-filtrados-fecha")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltradosPorFechaActual(
            @RequestParam(required = false) String dniAsesor,
            @RequestParam(required = false) String nombreAsesor,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {

        // Usar CompletableFuture con programación funcional
        return CompletableFuture
                .supplyAsync(() -> clienteResidencialService.obtenerClientesConUsuarioFiltradosPorFechaActual(
                        dniAsesor, nombreAsesor, numeroMovil, page, size))
                .exceptionally(ex -> {
                    // Manejar excepciones de forma funcional
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Error al obtener clientes por fecha actual: " + ex.getMessage(),
                                    null));
                })
                .join();
    }

    /**
     * Endpoint para obtener clientes con filtros personalizados
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono
     */
    @GetMapping("/con-usuario-filtrados")
    public ResponseEntity<GenericResponse<Map<String, Object>>> obtenerClientesConUsuarioFiltrados(
            @RequestParam(required = false) String dniAsesor,
            @RequestParam(required = false) String nombreAsesor,
            @RequestParam(required = false) String numeroMovil,
            @RequestParam(required = false) String fecha,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size) {

        // Usar CompletableFuture con programación funcional
        return CompletableFuture
                .supplyAsync(() -> clienteResidencialService.obtenerClientesConUsuarioFiltrados(
                        dniAsesor, nombreAsesor, numeroMovil, fecha, page, size))
                .exceptionally(ex -> {
                    // Manejar excepciones de forma funcional
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Error al obtener clientes filtrados: " + ex.getMessage(),
                                    null));
                })
                .join();
    }

    /**
     * Endpoint para exportar en formato MASIVO (todos los clientes en una sola
     * hoja).
     * Versión ultra-rápida con optimizaciones extremas para rendimiento.
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono.
     */
    @GetMapping("/exportar-excel-masivo")
    public ResponseEntity<?> exportarExcelMasivo() {
        // Función para crear headers HTTP para Excel
        Supplier<HttpHeaders> crearHeadersExcel = () -> {
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=clientes_residenciales_masivo.xlsx");
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            headers.set(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.set(HttpHeaders.PRAGMA, "no-cache");
            headers.set(HttpHeaders.EXPIRES, "0");
            return headers;
        };

        try {
            // Generar el Excel de forma asíncrona usando CompletableFuture con programación
            // funcional
            return CompletableFuture.supplyAsync(() -> clienteResidencialExcelService.generarExcelClientesMasivo())
                    .<ResponseEntity<?>>thenApply(excelData -> {
                        // Verificar si hay datos
                        if (excelData == null || excelData.length == 0) {
                            return ResponseEntity.ok()
                                    .body(new GenericResponse<>(
                                            GenericResponseConstants.SUCCESS,
                                            "No hay datos disponibles",
                                            null));
                        }

                        // Retornar el archivo Excel con los headers adecuados
                        return new ResponseEntity<>(excelData, crearHeadersExcel.get(), HttpStatus.OK);
                    })
                    .exceptionally(ex -> ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Error al generar el Excel masivo",
                                    ex.getMessage())))
                    .join();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error inesperado al generar el Excel masivo",
                            e.getMessage()));
        }
    }

    /**
     * Endpoint para exportar en formato INDIVIDUAL (un solo cliente).
     * Se busca el cliente por su "movilContacto".
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono.
     */
    @GetMapping("/exportar-excel-individual/{movil}")
    public ResponseEntity<?> exportarExcelIndividual(@PathVariable String movil) {
        // Validar el parámetro de entrada
        if (movil == null || movil.trim().isEmpty()) {
            return ResponseEntity.badRequest()
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "El número de móvil no puede estar vacío",
                            null));
        }

        try {
            // Usar CompletableFuture con programación funcional
            return CompletableFuture
                    .<ResponseEntity<?>>supplyAsync(() -> {
                        try {
                            return clienteResidencialService.exportarExcelPorMovil(movil);
                        } catch (Exception e) {
                            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                    .body(new GenericResponse<>(
                                            GenericResponseConstants.ERROR,
                                            "Error al generar el Excel individual: " + e.getMessage(),
                                            null));
                        }
                    })
                    .join();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error inesperado al generar el Excel individual",
                            e.getMessage()));
        }
    }

    /**
     * Endpoint para actualizar un cliente.
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono.
     */
    @PutMapping("/{id}")
    public ResponseEntity<GenericResponse<ClienteResidencial>> actualizarCliente(
            @PathVariable Long id,
            @RequestBody ClienteResidencial cliente) {

        try {
            // Validar parámetros de entrada
            if (id == null || cliente == null) {
                throw new MidasExceptions(MidasErrorMessage.CLIENTERESIDENCIAL_INVALID_DATA);
            }

            // Usar CompletableFuture con programación funcional
            ClienteResidencial clienteActualizado = CompletableFuture
                    .supplyAsync(() -> clienteResidencialService.actualizar(id, cliente))
                    .exceptionally(manejarExcepcionCliente)
                    .join();

            // Construir y retornar respuesta exitosa
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Cliente actualizado correctamente",
                            clienteActualizado));
        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    /**
     * Endpoint para actualizar solo la nota del agente comparador IA de un cliente.
     * Implementado con programación funcional y CompletableFuture para procesamiento asíncrono.
     */
    @PutMapping("/{id}/nota-agente-comparador-ia")
    public ResponseEntity<GenericResponse<ClienteResidencial>> actualizarNotaAgenteComparadorIA(
            @PathVariable Long id,
            @RequestBody Map<String, Object> requestBody) {

        try {
            // Validar parámetros de entrada
            if (id == null) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "El ID del cliente no puede estar vacío",
                                null));
            }

            // Extraer y validar la nota del request body
            Object notaObj = requestBody.get("notaAgenteComparadorIA");
            if (notaObj == null) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "La nota del agente comparador IA es requerida",
                                null));
            }

            BigDecimal nota;
            try {
                // Convertir a BigDecimal, soportando tanto String como Number
                if (notaObj instanceof String) {
                    nota = new BigDecimal((String) notaObj);
                } else if (notaObj instanceof Number) {
                    nota = BigDecimal.valueOf(((Number) notaObj).doubleValue());
                } else {
                    throw new NumberFormatException("Tipo de dato no válido");
                }

                // Validar rango (opcional: ajustar según tus necesidades)
                if (nota.compareTo(BigDecimal.ZERO) < 0 || nota.compareTo(new BigDecimal("999.99")) > 0) {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "La nota debe estar entre 0.00 y 999.99",
                                    null));
                }
            } catch (NumberFormatException e) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Formato de nota inválido. Debe ser un número válido",
                                null));
            }

            // Usar CompletableFuture con programación funcional
            ClienteResidencial clienteActualizado = CompletableFuture
                    .supplyAsync(() -> clienteResidencialService.actualizarNotaAgenteComparadorIA(id, nota))
                    .exceptionally(manejarExcepcionCliente)
                    .join();

            // Construir y retornar respuesta exitosa
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Nota del agente comparador IA actualizada correctamente",
                            clienteActualizado));

        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    /**
     * Endpoint para buscar clientes por número móvil
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono
     */
    @GetMapping("/buscar-por-movil/{numeroMovil}")
    public ResponseEntity<GenericResponse<List<ClienteResidencial>>> buscarClientesPorMovil(
            @PathVariable String numeroMovil) {

        try {
            // Validar parámetros de entrada
            if (numeroMovil == null || numeroMovil.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "El número móvil no puede estar vacío",
                                null));
            }

            // Usar CompletableFuture con programación funcional
            List<ClienteResidencial> clientes = CompletableFuture
                    .supplyAsync(() -> clienteResidencialService.buscarPorMovil(numeroMovil.trim()))
                    .exceptionally(manejarExcepcionLista)
                    .join();

            // Construir y retornar respuesta exitosa
            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            clientes.isEmpty() ? "No se encontraron clientes con ese número móvil" : "Clientes encontrados",
                            clientes));
        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    /**
     * Endpoint para verificar si un cliente ya tiene transcripción
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono
     */
    @GetMapping("/verificar-transcripcion/{numeroMovil}")
    public ResponseEntity<GenericResponse<Map<String, Object>>> verificarTranscripcion(
            @PathVariable String numeroMovil) {

        try {
            // Validar parámetros de entrada
            if (numeroMovil == null || numeroMovil.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "El número móvil no puede estar vacío",
                                null));
            }

            // Usar CompletableFuture con programación funcional
            List<ClienteResidencial> clientes = CompletableFuture
                    .supplyAsync(() -> clienteResidencialService.buscarPorMovil(numeroMovil.trim()))
                    .exceptionally(manejarExcepcionLista)
                    .join();

            // Verificar si se encontró el cliente
            if (clientes.isEmpty()) {
                Map<String, Object> resultado = new HashMap<>();
                resultado.put("tieneTranscripcion", false);
                resultado.put("clienteEncontrado", false);
                resultado.put("mensaje", "Cliente no encontrado");

                return ResponseEntity.ok(
                        new GenericResponse<>(
                                GenericResponseConstants.SUCCESS,
                                "Cliente no encontrado",
                                resultado));
            }

            // Tomar el primer cliente encontrado
            ClienteResidencial cliente = clientes.get(0);

            // Verificar si tiene transcripción
            boolean tieneTextoTranscripcion = cliente.getTextoTranscription() != null &&
                    !cliente.getTextoTranscription().trim().isEmpty();
            boolean tieneUrlDriveTranscripcion = cliente.getUrlDriveTranscripcion() != null &&
                    !cliente.getUrlDriveTranscripcion().trim().isEmpty();

            boolean tieneTranscripcion = tieneTextoTranscripcion || tieneUrlDriveTranscripcion;

            // Construir respuesta
            Map<String, Object> resultado = new HashMap<>();
            resultado.put("tieneTranscripcion", tieneTranscripcion);
            resultado.put("clienteEncontrado", true);
            resultado.put("clienteId", cliente.getId());
            resultado.put("nombreCliente", cliente.getNombresApellidos());
            resultado.put("tieneTextoTranscripcion", tieneTextoTranscripcion);
            resultado.put("tieneUrlDriveTranscripcion", tieneUrlDriveTranscripcion);

            if (tieneTranscripcion) {
                resultado.put("mensaje", "El cliente ya tiene una transcripción registrada");
                if (tieneUrlDriveTranscripcion) {
                    resultado.put("urlDriveTranscripcion", cliente.getUrlDriveTranscripcion());
                }
            } else {
                resultado.put("mensaje", "El cliente no tiene transcripción registrada");
            }

            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            tieneTranscripcion ? "Cliente tiene transcripción" : "Cliente sin transcripción",
                            resultado));

        } catch (MidasExceptions e) {
            throw e;
        } catch (Exception e) {
            throw new MidasExceptions(MidasErrorMessage.ERROR_INTERNAL);
        }
    }

    /**
     * Endpoint para exportar a Excel los clientes filtrados por una fecha dada.
     * Ejemplo: /api/clientes/exportar-excel-por-fecha?fecha=2025-03-13
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono.
     */
    @GetMapping("/exportar-excel-por-fecha")
    public ResponseEntity<?> exportarExcelPorFecha(@RequestParam("fecha") String fechaStr) {
        // Función para crear headers HTTP para Excel con fecha específica
        Function<LocalDate, HttpHeaders> crearHeadersExcel = fecha -> {
            String fechaArchivo = fecha.format(DateTimeFormatter.ISO_LOCAL_DATE);
            HttpHeaders headers = new HttpHeaders();
            headers.set(HttpHeaders.CONTENT_DISPOSITION,
                    "attachment; filename=clientes_residenciales_" + fechaArchivo + ".xlsx");
            headers.set(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            headers.set(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
            headers.set(HttpHeaders.PRAGMA, "no-cache");
            headers.set(HttpHeaders.EXPIRES, "0");
            return headers;
        };

        try {
            // Validar la fecha primero
            LocalDate fecha;
            try {
                fecha = LocalDate.parse(fechaStr, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Formato de fecha inválido. Use el formato yyyy-MM-dd",
                                null));
            }

            // Generar el Excel de forma asíncrona usando CompletableFuture
            return CompletableFuture
                    .supplyAsync(() -> clienteResidencialExcelService.generarExcelClientesPorFecha(fecha))
                    .<ResponseEntity<?>>thenApply(excelData -> {
                        // Verificar si hay datos
                        if (excelData == null || excelData.length == 0) {
                            return ResponseEntity.ok()
                                    .body(new GenericResponse<>(
                                            GenericResponseConstants.SUCCESS,
                                            "No hay datos para la fecha seleccionada",
                                            null));
                        }

                        // Retornar el archivo Excel con los headers adecuados
                        return new ResponseEntity<>(excelData, crearHeadersExcel.apply(fecha), HttpStatus.OK);
                    })
                    .exceptionally(ex -> ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "Error al generar el Excel por fecha: " + ex.getMessage(),
                                    null)))
                    .join();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error inesperado al generar el Excel por fecha",
                            e.getMessage()));
        }
    }

    /**
     * Endpoint para exportar a Excel los clientes filtrados por un rango de fechas.
     * Ejemplo:
     * /api/clientes/exportar-excel-por-rango-fechas?fechaInicio=2025-03-01&fechaFin=2025-03-31
     * Implementado con programación funcional y CompletableFuture para
     * procesamiento asíncrono.
     */
    @GetMapping("/exportar-excel-por-rango-fechas")
    public ResponseEntity<?> exportarExcelPorRangoFechas(
            @RequestParam("fechaInicio") String fechaInicioStr,
            @RequestParam("fechaFin") String fechaFinStr) {

        try {
            // Validar las fechas primero
            LocalDate fechaInicio, fechaFin;
            try {
                fechaInicio = LocalDate.parse(fechaInicioStr, DateTimeFormatter.ISO_LOCAL_DATE);
                fechaFin = LocalDate.parse(fechaFinStr, DateTimeFormatter.ISO_LOCAL_DATE);

                // Validar que la fecha de inicio no sea posterior a la fecha de fin
                if (fechaInicio.isAfter(fechaFin)) {
                    return ResponseEntity.badRequest()
                            .body(new GenericResponse<>(
                                    GenericResponseConstants.ERROR,
                                    "La fecha de inicio no puede ser posterior a la fecha de fin",
                                    null));
                }
            } catch (DateTimeParseException e) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Formato de fecha inválido. Use el formato yyyy-MM-dd",
                                null));
            }

            // Usar el servicio para generar el Excel
            try {
                return clienteResidencialService.exportarExcelPorRangoFechas(fechaInicioStr, fechaFinStr);
            } catch (MidasExceptions e) {
                return ResponseEntity.badRequest()
                        .body(new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                e.getMessage(),
                                null));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error inesperado al generar el Excel por rango de fechas",
                            e.getMessage()));
        }
    }
}