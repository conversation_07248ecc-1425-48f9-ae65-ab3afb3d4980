package com.midas.crm.repository;

import com.midas.crm.entity.Cuestionario;
import com.midas.crm.entity.RespuestaUsuario;
import com.midas.crm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RespuestaUsuarioRepository extends JpaRepository<RespuestaUsuario, Long> {
    List<RespuestaUsuario> findByUsuarioAndCuestionarioOrderByFechaInicioDesc(User usuario, Cuestionario cuestionario);
    List<RespuestaUsuario> findByUsuarioIdAndCuestionarioIdOrderByFechaInicioDesc(Long usuarioId, Long cuestionarioId);

    @Query("SELECT COUNT(ru) FROM RespuestaUsuario ru WHERE ru.usuario.id = :usuarioId AND ru.cuestionario.id = :cuestionarioId")
    int countByUsuarioIdAndCuestionarioId(Long usuarioId, Long cuestionarioId);

    @Query("SELECT ru FROM RespuestaUsuario ru WHERE ru.usuario.id = :usuarioId AND ru.cuestionario.id = :cuestionarioId AND ru.completado = true ORDER BY ru.puntajeObtenido DESC, ru.fechaFin ASC")
    List<RespuestaUsuario> findMejorIntentoByCuestionarioAndUsuario(Long usuarioId, Long cuestionarioId);

    @Query("SELECT ru FROM RespuestaUsuario ru WHERE ru.usuario.id = :usuarioId AND ru.cuestionario.leccion.seccion.modulo.curso.id = :cursoId AND ru.completado = true")
    List<RespuestaUsuario> findCompletadosByCursoAndUsuario(Long usuarioId, Long cursoId);

    @Query("SELECT COUNT(DISTINCT ru.cuestionario.id) FROM RespuestaUsuario ru WHERE ru.usuario.id = :usuarioId AND ru.cuestionario.leccion.seccion.modulo.curso.id = :cursoId AND ru.aprobado = true")
    Long countCuestionariosAprobadosByCursoAndUsuario(Long usuarioId, Long cursoId);

    @Query("SELECT COUNT(DISTINCT l.id) FROM Leccion l JOIN l.seccion s JOIN s.modulo m WHERE m.curso.id = :cursoId AND l.tipoLeccion = 'CUESTIONARIO'")
    Long countTotalCuestionariosByCurso(Long cursoId);
}
