package com.midas.crm.entity.DTO.cuestionario;

import com.midas.crm.entity.Pregunta.TipoPregunta;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PreguntaCreateDTO {
    @NotBlank(message = "El enunciado de la pregunta es obligatorio")
    private String enunciado;
    
    private String explicacion;
    
    @Min(value = 1, message = "El puntaje debe ser al menos 1")
    private Integer puntaje = 1;
    
    @NotNull(message = "El orden de la pregunta es obligatorio")
    private Integer orden;
    
    @NotNull(message = "El tipo de pregunta es obligatorio")
    private TipoPregunta tipo = TipoPregunta.OPCION_MULTIPLE;
    
    @NotNull(message = "El ID del cuestionario es obligatorio")
    private Long cuestionarioId;
    
    private List<RespuestaCreateDTO> respuestas;
}
