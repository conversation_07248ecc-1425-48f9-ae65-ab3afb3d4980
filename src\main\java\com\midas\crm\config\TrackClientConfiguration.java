package com.midas.crm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class TrackClientConfiguration {

    static final int CONNECT_TIMEOUT_MILLISECONDS = 8_000;
    static final int READ_TIMEOUT_MILLISECONDS = 8_000;
    
    @Bean("trackRestTemplate")
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory clientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        clientHttpRequestFactory.setConnectTimeout(CONNECT_TIMEOUT_MILLISECONDS);
        clientHttpRequestFactory.setReadTimeout(READ_TIMEOUT_MILLISECONDS);

        return new RestTemplate(clientHttpRequestFactory);
    }

}
