package com.midas.crm.entity.DTO.seccion;

import com.midas.crm.entity.DTO.leccion.LeccionDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SeccionDTO {
    private Long id;
    private String titulo;
    private String descripcion;
    private Integer orden;
    private Long moduloId;
    private String moduloTitulo;
    private String estado;
    private LocalDateTime fechaCreacion;
    private List<LeccionDTO> lecciones;
    private Boolean completado; // Indica si todas las lecciones están completadas
}
