package com.midas.crm.event;

import com.midas.crm.entity.ClienteResidencial;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Evento que se dispara cuando se crea un nuevo cliente
 */
@Getter
public class ClienteCreatedEvent extends ApplicationEvent {
    
    private final ClienteResidencial cliente;
    
    public ClienteCreatedEvent(Object source, ClienteResidencial cliente) {
        super(source);
        this.cliente = cliente;
    }
}
