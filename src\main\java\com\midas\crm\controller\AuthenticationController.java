package com.midas.crm.controller;

import com.midas.crm.entity.User;
import com.midas.crm.exceptions.MidasExceptions;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.security.jwt.JwtProvider;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.GenericResponseConstants;
import com.midas.crm.utils.MidasErrorMessage;
import io.jsonwebtoken.Claims;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("${api.route.authentication}")
public class AuthenticationController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtProvider jwtProvider;

    @Autowired
    private com.midas.crm.service.LoginService loginService;

    @Autowired
    private com.midas.crm.service.UserConnectionService userConnectionService;

    @Autowired
    private javax.sql.DataSource dataSource;

    /**
     * Libera conexiones inactivas del pool de conexiones
     * Método utilitario para evitar duplicación de código
     */
    private void evictIdleConnections() {
        Optional.ofNullable(dataSource)
            .filter(ds -> ds instanceof com.zaxxer.hikari.HikariDataSource)
            .map(ds -> (com.zaxxer.hikari.HikariDataSource) ds)
            .ifPresent(hikariDS -> {
                try {
                    hikariDS.getHikariPoolMXBean().softEvictConnections();
                } catch (Exception e) {
                    // Ignorar errores en la liberación de conexiones
                }
            });
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<GenericResponse<Map<String, String>>> refreshToken(HttpServletRequest request) {
        try {
            // Extraer claims (aunque el token esté expirado)
            Claims claims = jwtProvider.extractClaims(request);
            if (claims == null || claims.getSubject() == null) {

                return ResponseEntity.status(HttpStatus.OK).body(
                        new GenericResponse<>(
                                GenericResponseConstants.ERROR,
                                "Token inválido o no se pudo extraer información",
                                null
                        )
                );
            }

            String username = claims.getSubject();

            // Buscar usuario por username
            User user = userService.findByUsername(username)
                    .orElseThrow(() -> new UsernameNotFoundException("Usuario no encontrado"));

            // Verificar que el usuario esté activo
            if (!"A".equals(user.getEstado())) {
                throw new UsernameNotFoundException("Usuario inactivo");
            }

            // Generar nuevo token
            String nuevoToken = jwtProvider.generateToken(UserPrincipal.build(user));

            Map<String, String> data = new HashMap<>();
            data.put("token", nuevoToken);



            return ResponseEntity.ok(
                    new GenericResponse<>(
                            GenericResponseConstants.SUCCESS,
                            "Token renovado exitosamente",
                            data
                    )
            );

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.OK).body(
                    new GenericResponse<>(
                            GenericResponseConstants.ERROR,
                            "Error interno al renovar el token",
                            null
                    )
            );
        }
    }



    @PostMapping("sign-up")
    public ResponseEntity<GenericResponse<?>> signUp(@RequestBody User user) {
        if(userService.findByUsername(user.getUsername()).isPresent()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_ALREADY_EXISTS);
        }

        // Verificar si ya existe un usuario con el mismo DNI
        if(userService.findByDni(user.getDni()).isPresent()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_ALREADY_EXISTS);
        }

        // Si no se envía email o está vacío, generarlo a partir del username
        if(user.getEmail() == null || user.getEmail().isEmpty() || user.getEmail().trim().isEmpty()) {
            user.setEmail(user.getUsername() + "@midas.pe");

        }

        // Verificar el email sólo si se envió o se generó
        if(userService.findByEmail(user.getEmail()).isPresent()) {
            throw new MidasExceptions(MidasErrorMessage.USUARIO_ALREADY_EXISTS);
        }

        User savedUser = userService.saveUser(user);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Usuario creado exitosamente",
                        savedUser
                ));
    }

    @PostMapping("sign-in")
    public ResponseEntity<GenericResponse<Map<String, Object>>> signIn(@RequestBody User user) {
        // Enfoque funcional para el proceso de login
        return procesarLogin(user);
    }

    /**
     * Procesa el login de forma funcional
     * @param user Usuario con credenciales
     * @return Respuesta HTTP con resultado del login
     */
    private ResponseEntity<GenericResponse<Map<String, Object>>> procesarLogin(User user) {
        // Validar que el username no sea nulo
        if (user.getUsername() == null || user.getUsername().isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(
                new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Nombre de usuario requerido",
                    null
                )
            );
        }

        try {
            // Verificar si el usuario existe
            if (!loginService.userExists(user.getUsername())) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                    new GenericResponse<>(
                        GenericResponseConstants.ERROR,
                        "Usuario o contraseña incorrectos",
                        null
                    )
                );
            }

            try {
                // Proceso de login exitoso
                Map<String, Object> responseData = loginService.login(user);

                // Liberar conexiones inactivas para mejorar la gestión del pool
                evictIdleConnections();

                return ResponseEntity.ok(
                    new GenericResponse<>(
                        GenericResponseConstants.SUCCESS,
                        "Inicio de sesión exitoso",
                        responseData
                    )
                );
            } catch (BadCredentialsException | UsernameNotFoundException e) {
                // Credenciales incorrectas o usuario no encontrado
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
                    new GenericResponse<>(
                        GenericResponseConstants.ERROR,
                        "Usuario o contraseña incorrectos",
                        null
                    )
                );
            }
        } catch (Exception e) {
            // Error interno
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                new GenericResponse<>(
                    GenericResponseConstants.ERROR,
                    "Error interno del servidor",
                    null
                )
            );
        }
    }

    @PostMapping("sign-out")
    public ResponseEntity<GenericResponse<String>> signOut(@RequestBody Map<String, Object> payload) {
        // Enfoque funcional para el proceso de cierre de sesión
        return procesarCierreSesion(payload);
    }

    /**
     * Procesa el cierre de sesión de forma funcional
     * @param payload Datos del cierre de sesión
     * @return Respuesta HTTP con resultado del cierre de sesión
     */
    private ResponseEntity<GenericResponse<String>> procesarCierreSesion(Map<String, Object> payload) {
        // Verificar si el payload contiene el userId
        if (payload == null || !payload.containsKey("userId")) {
            return ResponseEntity.ok(
                new GenericResponse<String>(
                    GenericResponseConstants.ERROR,
                    "No se pudo identificar al usuario",
                    null
                )
            );
        }

        try {
            // Obtener y validar el ID del usuario
            Long userId = Long.valueOf(payload.get("userId").toString());
            if (userId <= 0) {
                return ResponseEntity.ok(
                    new GenericResponse<String>(
                        GenericResponseConstants.ERROR,
                        "ID de usuario inválido",
                        null
                    )
                );
            }

            // Desconectar al usuario
            userConnectionService.disconnectUser(userId);

            // Liberar conexiones inactivas del pool
            evictIdleConnections();

            return ResponseEntity.ok(
                new GenericResponse<String>(
                    GenericResponseConstants.SUCCESS,
                    "Sesión cerrada exitosamente",
                    null
                )
            );
        } catch (NumberFormatException e) {
            return ResponseEntity.ok(
                new GenericResponse<String>(
                    GenericResponseConstants.ERROR,
                    "ID de usuario inválido",
                    null
                )
            );
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                new GenericResponse<String>(
                    GenericResponseConstants.ERROR,
                    "Error interno del servidor",
                    null
                )
            );
        }
    }

}