package com.midas.crm.controller;

import com.midas.crm.utils.GenericResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Controlador para verificar la salud del servidor
 * Útil para que el frontend verifique si el servidor está disponible antes de intentar reconectar WebSocket
 */
@RestController
@RequestMapping("/api/health")
@Slf4j
public class HealthController {

    /**
     * Endpoint simple para verificar si el servidor está disponible
     * @return ResponseEntity con información básica del servidor
     */
    @GetMapping
    public ResponseEntity<GenericResponse<Map<String, Object>>> checkHealth() {
        try {
            Map<String, Object> healthInfo = new HashMap<>();
            healthInfo.put("status", "UP");
            healthInfo.put("timestamp", LocalDateTime.now());
            healthInfo.put("server", "Midas CRM Backend");
            healthInfo.put("websocket", "available");

            GenericResponse<Map<String, Object>> response = new GenericResponse<>(
                    1,
                    "Servidor disponible",
                    healthInfo
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error en health check: {}", e.getMessage());

            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("status", "DOWN");
            errorInfo.put("timestamp", LocalDateTime.now());
            errorInfo.put("error", e.getMessage());

            GenericResponse<Map<String, Object>> response = new GenericResponse<>(
                    0,
                    "Error en servidor",
                    errorInfo
            );

            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Endpoint específico para verificar el estado del WebSocket
     * @return ResponseEntity con información del WebSocket
     */
    @GetMapping("/websocket")
    public ResponseEntity<GenericResponse<Map<String, Object>>> checkWebSocketHealth() {
        try {
            Map<String, Object> wsInfo = new HashMap<>();
            wsInfo.put("websocket_status", "available");
            wsInfo.put("endpoint", "/ws");
            wsInfo.put("protocols", new String[]{"websocket", "xhr-streaming", "xhr-polling"});
            wsInfo.put("heartbeat_interval", 20000);
            wsInfo.put("disconnect_delay", 60000);
            wsInfo.put("timestamp", LocalDateTime.now());

            GenericResponse<Map<String, Object>> response = new GenericResponse<>(
                    1,
                    "WebSocket disponible",
                    wsInfo
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error en WebSocket health check: {}", e.getMessage());

            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("websocket_status", "error");
            errorInfo.put("timestamp", LocalDateTime.now());
            errorInfo.put("error", e.getMessage());

            GenericResponse<Map<String, Object>> response = new GenericResponse<>(
                    0,
                    "Error en WebSocket",
                    errorInfo
            );

            return ResponseEntity.status(500).body(response);
        }
    }
}
