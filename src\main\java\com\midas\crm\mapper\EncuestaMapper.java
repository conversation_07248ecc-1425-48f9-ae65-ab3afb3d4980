package com.midas.crm.mapper;

import com.midas.crm.entity.DTO.encuesta.EncuestaCreateDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaDTO;
import com.midas.crm.entity.DTO.encuesta.EncuestaUpdateDTO;
import com.midas.crm.entity.DTO.encuesta.PreguntaEncuestaDTO;
import com.midas.crm.entity.Encuesta;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper para convertir entre entidades Encuesta y sus DTOs
 */
public final class EncuestaMapper {

    private EncuestaMapper() {}

    /**
     * Convierte un DTO de creación a una entidad Encuesta
     */
    public static Encuesta toEntity(EncuestaCreateDTO dto, User creador) {
        Encuesta encuesta = new Encuesta();
        encuesta.setTitulo(dto.getTitulo());
        encuesta.setDescripcion(dto.getDescripcion());
        encuesta.setFechaInicio(dto.getFechaInicio());
        encuesta.setFechaFin(dto.getFechaFin());
        encuesta.setTiempoLimite(dto.getTiempoLimite());
        encuesta.setEsAnonima(dto.getEsAnonima());
        encuesta.setMostrarResultados(dto.getMostrarResultados());
        encuesta.setTipoAsignacion(dto.getTipoAsignacion());
        encuesta.setCreador(creador);
        encuesta.setEstado("A");
        return encuesta;
    }

    /**
     * Convierte una entidad Encuesta a un DTO
     */
    public static EncuestaDTO toDTO(Encuesta encuesta) {
        if (encuesta == null) return null;

        EncuestaDTO dto = new EncuestaDTO();
        dto.setId(encuesta.getId());
        dto.setTitulo(encuesta.getTitulo());
        dto.setDescripcion(encuesta.getDescripcion());
        dto.setFechaInicio(encuesta.getFechaInicio());
        dto.setFechaFin(encuesta.getFechaFin());
        dto.setTiempoLimite(encuesta.getTiempoLimite());
        dto.setEsAnonima(encuesta.getEsAnonima());
        dto.setMostrarResultados(encuesta.getMostrarResultados());
        dto.setTipoAsignacion(encuesta.getTipoAsignacion());
        dto.setEstado(encuesta.getEstado());
        dto.setFechaCreacion(encuesta.getFechaCreacion());
        dto.setFechaActualizacion(encuesta.getFechaActualizacion());

        // Información de sede (si aplica)
        if (encuesta.getSede() != null) {
            dto.setSedeId(encuesta.getSede().getId());
            dto.setSedeNombre(encuesta.getSede().getNombre());
        }

        // Información de coordinador (si aplica)
        if (encuesta.getCoordinador() != null) {
            dto.setCoordinadorId(encuesta.getCoordinador().getId());
            dto.setCoordinadorNombre(encuesta.getCoordinador().getNombre() + " " + encuesta.getCoordinador().getApellido());
        }

        // Información de usuario específico (si aplica)
        if (encuesta.getUsuario() != null) {
            dto.setUsuarioId(encuesta.getUsuario().getId());
            dto.setUsuarioNombre(encuesta.getUsuario().getNombre() + " " + encuesta.getUsuario().getApellido());
        }

        // Información del creador
        if (encuesta.getCreador() != null) {
            dto.setCreador(UserMapper.toDTO(encuesta.getCreador()));
        }

        return dto;
    }

    /**
     * Convierte una entidad Encuesta a un DTO incluyendo sus preguntas
     */
    public static EncuestaDTO toDTOWithPreguntas(Encuesta encuesta, List<PreguntaEncuestaDTO> preguntas) {
        EncuestaDTO dto = toDTO(encuesta);
        if (dto != null) {
            dto.setPreguntas(preguntas);
        }
        return dto;
    }

    /**
     * Actualiza una entidad Encuesta con los datos de un DTO de actualización
     */
    public static void updateEntityFromDTO(Encuesta encuesta, EncuestaUpdateDTO dto) {
        if (dto.getTitulo() != null) {
            encuesta.setTitulo(dto.getTitulo());
        }
        if (dto.getDescripcion() != null) {
            encuesta.setDescripcion(dto.getDescripcion());
        }
        if (dto.getFechaInicio() != null) {
            encuesta.setFechaInicio(dto.getFechaInicio());
        }
        if (dto.getFechaFin() != null) {
            encuesta.setFechaFin(dto.getFechaFin());
        }
        if (dto.getTiempoLimite() != null) {
            encuesta.setTiempoLimite(dto.getTiempoLimite());
        }
        if (dto.getEsAnonima() != null) {
            encuesta.setEsAnonima(dto.getEsAnonima());
        }
        if (dto.getMostrarResultados() != null) {
            encuesta.setMostrarResultados(dto.getMostrarResultados());
        }
        if (dto.getTipoAsignacion() != null) {
            encuesta.setTipoAsignacion(dto.getTipoAsignacion());
        }
        if (dto.getEstado() != null) {
            encuesta.setEstado(dto.getEstado());
        }
    }
}
